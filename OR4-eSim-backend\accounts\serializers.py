import logging
import re

from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from rest_framework import serializers

from .models import PasswordResetToken, User, UserProfile


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "role",
            "is_active",
            "date_joined",
            "last_login",
        ]
        read_only_fields = ["id", "date_joined", "last_login"]


class SignupSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, min_length=8)
    confirm_password = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = [
            "email",
            "first_name",
            "last_name",
            "role",
            "password",
            "confirm_password",
        ]

    def validate(self, attrs):
        # Check if passwords match
        if attrs["password"] != attrs["confirm_password"]:
            raise serializers.ValidationError("Passwords don't match")

        # Only allow reseller and public_user roles for signup
        if attrs.get("role") not in ["reseller", "public_user"]:
            raise serializers.ValidationError(
                "Only resellers and public users can sign up"
            )

        # Check if email already exists
        if User.objects.filter(email=attrs["email"]).exists():
            raise serializers.ValidationError("A user with this email already exists")

        return attrs

    def create(self, validated_data):
        validated_data.pop("confirm_password")
        password = validated_data.pop("password")

        user = User.objects.create(**validated_data)
        user.set_password(password)
        user.save()
        return user


class EditProfileSerializer(serializers.Serializer):
    """
    Comprehensive serializer for editing user profile
    Handles partial updates for both User and UserProfile models
    """

    # User fields
    email = serializers.EmailField(required=False, allow_blank=True)
    first_name = serializers.CharField(max_length=30, required=False, allow_blank=True)
    last_name = serializers.CharField(max_length=30, required=False, allow_blank=True)
    phone_number = serializers.CharField(
        max_length=15, required=False, allow_blank=True
    )

    # UserProfile fields
    date_of_birth = serializers.DateField(required=False, allow_null=True)
    gender = serializers.ChoiceField(
        choices=[("male", "Male"), ("female", "Female"), ("other", "Other")],
        required=False,
        allow_blank=True,
    )
    address = serializers.CharField(required=False, allow_blank=True)
    city = serializers.CharField(max_length=100, required=False, allow_blank=True)
    state = serializers.CharField(max_length=100, required=False, allow_blank=True)
    country = serializers.CharField(max_length=100, required=False, allow_blank=True)
    postal_code = serializers.CharField(max_length=20, required=False, allow_blank=True)
    emergency_contact_name = serializers.CharField(
        max_length=100, required=False, allow_blank=True
    )
    emergency_contact_phone = serializers.CharField(
        max_length=15, required=False, allow_blank=True
    )
    preferences = serializers.JSONField(required=False, default=dict)

    # Profile image (handled separately)
    profile_image = serializers.FileField(required=False, allow_null=True)

    def validate_email(self, value):
        """Validate email uniqueness"""
        if value:
            user = self.context["request"].user
            if User.objects.exclude(pk=user.pk).filter(email=value).exists():
                raise serializers.ValidationError("This email is already in use")
        return value

    def validate_phone_number(self, value):
        """Validate phone number format"""
        if value:
            phone_regex = re.compile(r"^\+?1?\d{9,15}$")
            if not phone_regex.match(value):
                raise serializers.ValidationError(
                    "Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
                )
        return value

    def validate_profile_image(self, value):
        """Validate profile image file"""
        if value:
            # Check file type
            allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
            if value.content_type not in allowed_types:
                raise serializers.ValidationError(
                    "Invalid file type. Only JPEG, PNG, and GIF are allowed"
                )

            # Check file size (max 5MB)
            if value.size > 5 * 1024 * 1024:
                raise serializers.ValidationError(
                    "File size too large. Maximum size is 5MB"
                )

        return value

    def update(self, instance, validated_data):
        """Update user and profile with partial data"""
        user = instance
        profile_image = validated_data.pop("profile_image", None)

        # Update User model fields
        user_fields = ["email", "first_name", "last_name", "phone_number"]
        for field in user_fields:
            if field in validated_data and validated_data[field] is not None:
                setattr(user, field, validated_data[field])

        user.save()

        # Get or create UserProfile
        user_profile, created = UserProfile.objects.get_or_create(user=user)

        # Update UserProfile model fields
        profile_fields = [
            "date_of_birth",
            "gender",
            "address",
            "city",
            "state",
            "country",
            "postal_code",
            "emergency_contact_name",
            "emergency_contact_phone",
            "preferences",
        ]

        for field in profile_fields:
            if field in validated_data and validated_data[field] is not None:
                setattr(user_profile, field, validated_data[field])

        # Handle profile image upload if provided
        if profile_image is not None:
            try:
                user_full_name = f"{user.first_name} {user.last_name}".strip()
                public_url, file_path = firebase_storage.upload_profile_image(
                    profile_image, user.id, user_full_name
                )
                user_profile.profile_image_url = public_url
            except Exception as e:
                logger = logging.getLogger(__name__)
                logger.error(f"Profile image upload failed: {str(e)}")
                # Don't fail the entire update if image upload fails
                # Keep the existing image URL

        user_profile.save()

        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """Simple user update serializer"""

    class Meta:
        model = User
        fields = ["first_name", "last_name", "email"]

    def validate_email(self, value):
        user = self.context["request"].user
        if User.objects.exclude(pk=user.pk).filter(email=value).exists():
            raise serializers.ValidationError("This email is already in use")
        return value


class UserProfileSerializer(serializers.ModelSerializer):
    """Simple user profile serializer"""

    user = UserSerializer(read_only=True)

    class Meta:
        model = UserProfile
        fields = [
            "id",
            "user",
            "date_of_birth",
            "gender",
            "address",
            "city",
            "state",
            "postal_code",
            "country",
            "profile_image_url",
            "emergency_contact_name",
            "emergency_contact_phone",
            "preferences",
        ]
        read_only_fields = ["id", "user"]


class LoginSerializer(serializers.Serializer):
    """Simple login serializer"""

    email = serializers.EmailField()
    password = serializers.CharField(max_length=128, write_only=True)

    def validate(self, attrs):
        email = attrs.get("email")
        password = attrs.get("password")

        if email and password:
            # Try to authenticate with email
            user = authenticate(email=email, password=password)
            if not user:
                raise serializers.ValidationError("Invalid email or password")
            if not user.is_active:
                raise serializers.ValidationError("User account is disabled")
            attrs["user"] = user
        else:
            raise serializers.ValidationError("Must include email and password")

        return attrs


class PasswordResetRequestSerializer(serializers.Serializer):
    """Simple password reset request serializer"""

    email = serializers.EmailField()

    def validate_email(self, value):
        if not User.objects.filter(email=value, is_active=True).exists():
            raise serializers.ValidationError("No active user found with this email")
        return value


class PasswordResetConfirmSerializer(serializers.Serializer):
    """Simple password reset confirmation serializer"""

    email = serializers.EmailField()
    new_password = serializers.CharField(min_length=8, write_only=True)
    confirm_password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        # Check if passwords match
        if attrs["new_password"] != attrs["confirm_password"]:
            raise serializers.ValidationError("Passwords don't match")

        # Check if user exists
        email = attrs.get("email")
        try:
            user = User.objects.get(email=email, is_active=True)
            attrs["user"] = user
        except User.DoesNotExist:
            raise serializers.ValidationError("No active user found with this email")

        return attrs


class PasswordChangeSerializer(serializers.Serializer):
    """Simple password change serializer"""

    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(min_length=8, write_only=True)
    confirm_password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        if attrs["new_password"] != attrs["confirm_password"]:
            raise serializers.ValidationError("Passwords don't match")
        return attrs

    def validate_old_password(self, value):
        user = self.context["request"].user
        if not user.check_password(value):
            raise serializers.ValidationError("Invalid old password")
        return value


class PasswordResetTokenSerializer(serializers.ModelSerializer):
    """Simple password reset token serializer"""

    class Meta:
        model = PasswordResetToken
        fields = ["id", "user", "token", "created_at", "expires_at", "is_used"]
        read_only_fields = ["id", "created_at", "expires_at"]
