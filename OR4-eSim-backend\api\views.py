import logging
from datetime import timed<PERSON>ta

from django.conf import settings
from django.db.models import Avg, Count, Sum
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

# Import models
from accounts.models import User, UserProfile
from api.firebase_storage import firebase_storage
from api.utils import create_error_response, create_success_response
from clients.models import Client, PublicUser
from esim_management.models import ESIM
from orders.models import Order
from payments.models import Payment
from resellers.models import Reseller

logger = logging.getLogger(__name__)


@api_view(["GET"])
def dashboard_view(request):
    """Dashboard API endpoint"""
    if not request.user.is_authenticated:
        return Response(
            create_error_response("Authentication required"),
            status=status.HTTP_401_UNAUTHORIZED,
        )

    # Get user role
    user_role = getattr(request.user, "role", "unknown")

    if user_role == "admin":
        data = _get_admin_dashboard()
    elif user_role == "reseller":
        data = _get_reseller_dashboard(request.user)
    else:
        data = _get_client_dashboard(request.user)

    return Response(
        create_success_response(data=data, message="Dashboard data retrieved")
    )


def _get_admin_dashboard():
    """
    Get comprehensive admin dashboard data with real database queries.

    Returns:
        dict: Complete dashboard data including metrics, graphs, and activities
    """
    try:

        now = timezone.now()
        today = now.date()
        this_month = now.replace(day=1)

        # ===== KEY METRICS =====

        # Total Users (App-based SIM buyers) - Safe query
        try:
            total_public_users = PublicUser.objects.filter(is_active=True).count()
        except Exception:
            total_public_users = 0

        # Total Resellers - Safe query
        try:
            total_resellers = Reseller.objects.filter(is_suspended=False).count()
        except Exception:
            total_resellers = 0

        # Total Clients (under resellers) - Safe query
        try:
            total_clients = Client.objects.filter(is_active=True).count()
        except Exception:
            total_clients = 0

        # Daily SIM Orders - Safe query
        try:
            daily_orders = Order.objects.filter(
                created_at__date=today, order_type="esim"
            ).count()
        except Exception:
            daily_orders = 0

        # Monthly SIM Orders - Safe query
        try:
            monthly_orders = Order.objects.filter(
                created_at__gte=this_month, order_type="esim"
            ).count()
        except Exception:
            monthly_orders = 0

        # Revenue generated (completed payments) - Safe query
        try:
            total_revenue = (
                Payment.objects.filter(status="completed").aggregate(
                    total=Sum("amount")
                )["total"]
                or 0
            )
        except Exception:
            total_revenue = 0

        # Monthly revenue - Safe query
        try:
            monthly_revenue = (
                Payment.objects.filter(
                    status="completed", created_at__gte=this_month
                ).aggregate(total=Sum("amount"))["total"]
                or 0
            )
        except Exception:
            monthly_revenue = 0

        # Active eSIMs - Safe query
        try:
            active_esims = ESIM.objects.filter(
                status__in=["assigned", "activated"]
            ).count()
        except Exception:
            active_esims = 0

        # ===== SALES TRENDS =====

        # Last 30 days sales trend - Safe query
        sales_trend = []
        try:
            for i in range(30):
                date = today - timedelta(days=i)
                daily_sales = Order.objects.filter(
                    created_at__date=date, order_type="esim"
                ).aggregate(total_orders=Count("id"), total_revenue=Sum("total_amount"))
                sales_trend.append(
                    {
                        "date": date.isoformat(),
                        "orders": daily_sales["total_orders"] or 0,
                        "revenue": float(daily_sales["total_revenue"] or 0),
                    }
                )
            sales_trend.reverse()  # Oldest to newest
        except Exception:
            # If sales trend fails, create empty trend
            sales_trend = []
            for i in range(30):
                date = today - timedelta(days=i)
                sales_trend.append(
                    {"date": date.isoformat(), "orders": 0, "revenue": 0.0}
                )
            sales_trend.reverse()

        # ===== TOP PERFORMING RESELLERS =====

        top_resellers_data = []
        try:
            # Use values() to avoid attribute conflicts
            top_resellers_raw = (
                Reseller.objects.filter(is_suspended=False)
                .annotate(
                    total_orders=Count("orders"),
                    total_revenue=Sum("orders__total_amount"),
                    total_clients=Count("clients"),
                )
                .values(
                    "id",
                    "user__first_name",
                    "user__last_name",
                    "user__email",
                    "total_orders",
                    "total_revenue",
                    "total_clients",
                )
                .order_by("-total_revenue")[:10]
            )

            for reseller_data in top_resellers_raw:
                try:
                    total_orders = reseller_data.get("total_orders", 0) or 0
                    total_revenue = reseller_data.get("total_revenue", 0) or 0
                    total_clients = reseller_data.get("total_clients", 0) or 0

                    top_resellers_data.append(
                        {
                            "id": reseller_data["id"],
                            "name": (
                                f"{reseller_data['user__first_name']} "
                                f"{reseller_data['user__last_name']}"
                            ),
                            "email": reseller_data["user__email"],
                            "total_orders": total_orders,
                            "total_revenue": float(total_revenue),
                            "total_clients": total_clients,
                        }
                    )
                except Exception as reseller_error:
                    logger.error(
                        f"Error processing reseller data: {str(reseller_error)}"
                    )
                    continue

        except Exception as e:
            logger.error(f"Error in top resellers query: {str(e)}")
            # If annotation fails completely, return empty list
            top_resellers_data = []

        # ===== LATEST ACTIVITIES FEED =====

        # Recent orders - Safe query
        try:
            recent_orders = Order.objects.select_related(
                "public_user", "client", "reseller"
            ).order_by("-created_at")[:10]
        except Exception:
            recent_orders = []

        recent_activities = []
        for order in recent_orders:
            customer_name = "Unknown"
            if order.public_user:
                customer_name = (
                    f"{order.public_user.user.first_name} "
                    f"{order.public_user.user.last_name}"
                )
            elif order.client:
                customer_name = f"{order.client.full_name}"

            reseller_name = "Direct"
            if order.reseller:
                reseller_name = (
                    f"{order.reseller.user.first_name} "
                    f"{order.reseller.user.last_name}"
                )

            recent_activities.append(
                {
                    "type": "order",
                    "id": order.id,
                    "order_number": order.order_number,
                    "customer_name": customer_name,
                    "reseller_name": reseller_name,
                    "product_name": order.product_name,
                    "amount": float(order.total_amount),
                    "status": order.status,
                    "timestamp": order.created_at.isoformat(),
                }
            )

        # Recent user registrations - Safe query
        try:
            recent_users = User.objects.filter(
                created_at__gte=now - timedelta(days=7)
            ).order_by("-created_at")[:5]
        except Exception:
            recent_users = []

        for user in recent_users:
            recent_activities.append(
                {
                    "type": "registration",
                    "id": user.id,
                    "user_name": f"{user.first_name} {user.last_name}",
                    "email": user.email,
                    "role": user.role,
                    "timestamp": user.created_at.isoformat(),
                }
            )

        # Recent eSIM activations - Safe query
        try:
            recent_esims = (
                ESIM.objects.filter(
                    activated_at__isnull=False,
                    activated_at__gte=now - timedelta(days=7),
                )
                .select_related("plan", "client", "public_user")
                .order_by("-activated_at")[:5]
            )
        except Exception:
            recent_esims = []

        for esim in recent_esims:
            user_name = "Unknown"
            if esim.client:
                user_name = f"{esim.client.full_name}"
            elif esim.public_user:
                user_name = (
                    f"{esim.public_user.user.first_name} "
                    f"{esim.public_user.user.last_name}"
                )

            recent_activities.append(
                {
                    "type": "esim_activation",
                    "id": esim.id,
                    "user_name": user_name,
                    "plan_name": esim.plan.name,
                    "country": esim.plan.country,
                    "timestamp": esim.activated_at.isoformat(),
                }
            )

        # Sort activities by timestamp (newest first)
        recent_activities.sort(key=lambda x: x["timestamp"], reverse=True)
        recent_activities = recent_activities[:20]  # Limit to 20 most recent

        # ===== ADDITIONAL METRICS =====

        # Average order value
        avg_order_value = (
            Order.objects.filter(order_type="esim").aggregate(avg=Avg("total_amount"))[
                "avg"
            ]
            or 0
        )

        # Conversion rate (orders vs users)
        conversion_rate = 0
        if total_public_users > 0:
            conversion_rate = (
                total_public_users / (total_public_users + total_clients)
            ) * 100

        # ===== COMPILE DASHBOARD DATA =====

        dashboard_data = {
            # Key Metrics
            "key_metrics": {
                "total_public_users": total_public_users,
                "total_resellers": total_resellers,
                "total_clients": total_clients,
                "daily_orders": daily_orders,
                "monthly_orders": monthly_orders,
                "total_revenue": float(total_revenue),
                "monthly_revenue": float(monthly_revenue),
                "active_esims": active_esims,
                "avg_order_value": float(avg_order_value),
                "conversion_rate": round(conversion_rate, 2),
            },
            # Sales Trends Graph
            "sales_trends": {"period": "last_30_days", "data": sales_trend},
            # Top Performing Resellers
            "top_resellers": {"period": "all_time", "data": top_resellers_data},
            # Latest Activities Feed
            "recent_activities": {"period": "last_7_days", "data": recent_activities},
            # System Status
            "system_status": {
                "status": "healthy",
                "last_updated": now.isoformat(),
                "database_connected": True,
                "celery_workers": True,  # TODO: Add actual health check
            },
        }

        return dashboard_data

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Admin dashboard error: {str(e)}", exc_info=True)

        # Return a more detailed error in debug mode
        error_details = str(e) if settings.DEBUG else "Internal server error"

        return {
            "error": "Failed to load dashboard data",
            "details": error_details,
            "error_type": type(e).__name__,
        }


def _get_reseller_dashboard(user):
    """Get reseller dashboard data"""
    try:
        # TODO: Implement actual reseller dashboard logic
        # This would include reseller-specific statistics

        dashboard_data = {
            "total_clients": 0,
            "total_orders": 0,
            "total_revenue": 0,
            "commission_earned": 0,
            "recent_orders": [],
            "top_clients": [],
        }

        return dashboard_data
    except Exception as e:
        return {"error": str(e)}


def _get_client_dashboard(user):
    """Get client dashboard data"""
    try:
        # TODO: Implement actual client dashboard logic
        # This would include client-specific statistics

        dashboard_data = {
            "total_orders": 0,
            "active_esims": 0,
            "total_spent": 0,
            "recent_orders": [],
            "esim_status": [],
        }

        return dashboard_data
    except Exception as e:
        return {"error": str(e)}


@api_view(["POST"])
def upload_profile_image(request):
    """Upload profile image to Firebase Storage"""
    if not request.user.is_authenticated:
        return Response(
            create_error_response("Authentication required"),
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        # Check if image file is provided
        if "image" not in request.FILES:
            return Response(
                create_error_response("Image file is required"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        image_file = request.FILES["image"]

        # Validate file type
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
        if image_file.content_type not in allowed_types:
            return Response(
                create_error_response(
                    "Invalid file type. Only JPEG, PNG, and GIF are allowed"
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate file size (max 5MB)
        if image_file.size > 5 * 1024 * 1024:
            return Response(
                create_error_response("File size too large. Maximum size is 5MB"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Upload to Firebase Storage
        user_full_name = f"{request.user.first_name} {request.user.last_name}".strip()
        public_url, file_path = firebase_storage.upload_profile_image(
            image_file, request.user.id, user_full_name
        )

        # Update user profile with image URL
        user_profile, created = UserProfile.objects.get_or_create(user=request.user)
        user_profile.profile_image_url = public_url
        user_profile.save()

        return Response(
            create_success_response(
                data={
                    "image_url": public_url,
                    "file_path": file_path,
                    "user_id": request.user.id,
                },
                message="Profile image uploaded successfully",
            )
        )

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Profile image upload error: {str(e)}", exc_info=True)
        return Response(
            create_error_response(f"Failed to upload image: {str(e)}"),
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["POST"])
def upload_document_image(request):
    """Upload document image to Firebase Storage"""
    if not request.user.is_authenticated:
        return Response(
            create_error_response("Authentication required"),
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        # Check if image file is provided
        if "image" not in request.FILES:
            return Response(
                create_error_response("Image file is required"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if document type is provided
        document_type = request.data.get("document_type")
        if not document_type:
            return Response(
                create_error_response("Document type is required"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        image_file = request.FILES["image"]

        # Validate file type
        allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
        if image_file.content_type not in allowed_types:
            return Response(
                create_error_response(
                    "Invalid file type. Only JPEG, PNG, and GIF are allowed"
                ),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate file size (max 5MB)
        if image_file.size > 5 * 1024 * 1024:
            return Response(
                create_error_response("File size too large. Maximum size is 5MB"),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Upload to Firebase Storage
        user_full_name = f"{request.user.first_name} {request.user.last_name}".strip()
        public_url, file_path = firebase_storage.upload_document_image(
            image_file, document_type, request.user.id, user_full_name
        )

        return Response(
            create_success_response(
                data={
                    "image_url": public_url,
                    "file_path": file_path,
                    "document_type": document_type,
                    "user_id": request.user.id,
                },
                message="Document image uploaded successfully",
            )
        )

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Document image upload error: {str(e)}", exc_info=True)
        return Response(
            create_error_response(f"Failed to upload document: {str(e)}"),
            status=status.HTTP_400_BAD_REQUEST,
        )


@api_view(["DELETE"])
def delete_profile_image(request):
    """Delete profile image"""
    if not request.user.is_authenticated:
        return Response(
            create_error_response("Authentication required"),
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        # TODO: Implement actual image deletion logic
        # This would use Firebase Storage

        return Response(
            create_success_response(message="Profile image deleted successfully")
        )
    except Exception as e:
        return Response(
            create_error_response(f"Failed to delete image: {str(e)}"),
            status=status.HTTP_400_BAD_REQUEST,
        )


# Simple test view for Swagger
@api_view(["GET"])
@permission_classes([AllowAny])
def test_view(request):
    """Test view for Swagger"""
    return Response(
        create_success_response(
            data={"message": "Hello World"}, message="Test endpoint working"
        )
    )


# Test comment
# Test comment
