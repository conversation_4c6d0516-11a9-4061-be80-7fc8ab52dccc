from rest_framework import serializers

from .models import ESIM, ESIMDelivery, ESIMPlan, ESIMUsage, TraveRoamWebhook


class ESIMPlanSerializer(serializers.ModelSerializer):
    """ESIM plan serializer"""

    class Meta:
        model = ESIMPlan
        fields = [
            "id",
            "name",
            "description",
            "country",
            "region",
            "data_volume",
            "validity_days",
            "plan_type",
            "base_price",
            "reseller_price",
            "public_price",
            "traveroam_plan_id",
            "is_active",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class ESIMPlanCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ESIM plans"""

    class Meta:
        model = ESIMPlan
        fields = [
            "name",
            "description",
            "country",
            "region",
            "data_volume",
            "validity_days",
            "plan_type",
            "base_price",
            "reseller_price",
            "public_price",
            "traveroam_plan_id",
            "is_active",
        ]


class ESIMSerializer(serializers.ModelSerializer):
    """ESIM serializer"""

    plan = serializers.SerializerMethodField()
    reseller = serializers.SerializerMethodField()
    client = serializers.SerializerMethodField()
    public_user = serializers.SerializerMethodField()
    usage_summary = serializers.SerializerMethodField()

    class Meta:
        model = ESIM
        fields = [
            "id",
            "plan",
            "reseller",
            "client",
            "public_user",
            "status",
            "qr_code",
            "activation_code",
            "traveroam_esim_id",
            "traveroam_order_id",
            "data_used",
            "data_remaining",
            "created_at",
            "assigned_at",
            "activated_at",
            "expires_at",
            "updated_at",
            "usage_summary",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_plan(self, obj):
        return ESIMPlanSerializer(obj.plan).data

    def get_reseller(self, obj):
        if obj.reseller:
            from resellers.serializers import ResellerSerializer

            return ResellerSerializer(obj.reseller).data
        return None

    def get_client(self, obj):
        if obj.client:
            from clients.serializers import ClientSerializer

            return ClientSerializer(obj.client).data
        return None

    def get_public_user(self, obj):
        if obj.public_user:
            from clients.serializers import PublicUserSerializer

            return PublicUserSerializer(obj.public_user).data
        return None

    def get_usage_summary(self, obj):
        usage = obj.usage_logs.all()
        total_data_used = (
            usage.aggregate(total=serializers.Sum("data_used"))["total"] or 0
        )
        return {
            "total_data_used": total_data_used,
            "usage_count": usage.count(),
            "last_usage": (
                usage.order_by("-timestamp").first().timestamp
                if usage.exists()
                else None
            ),
        }


class ESIMCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ESIMs"""

    class Meta:
        model = ESIM
        fields = ["plan", "reseller", "client", "public_user"]


class ESIMUsageSerializer(serializers.ModelSerializer):
    """ESIM usage serializer"""

    esim = serializers.SerializerMethodField()

    class Meta:
        model = ESIMUsage
        fields = ["id", "esim", "data_used", "location", "timestamp", "webhook_data"]
        read_only_fields = ["id"]

    def get_esim(self, obj):
        return ESIMSerializer(obj.esim).data


class ESIMUsageCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ESIM usage records"""

    class Meta:
        model = ESIMUsage
        fields = ["esim", "data_used", "location", "timestamp", "webhook_data"]


class TraveRoamWebhookSerializer(serializers.ModelSerializer):
    """TraveRoam webhook serializer"""

    class Meta:
        model = TraveRoamWebhook
        fields = [
            "id",
            "webhook_type",
            "esim",
            "payload",
            "processed",
            "processed_at",
            "created_at",
        ]
        read_only_fields = ["id", "created_at", "processed_at"]


class ESIMDeliverySerializer(serializers.ModelSerializer):
    """ESIM delivery serializer"""

    esim = serializers.SerializerMethodField()

    class Meta:
        model = ESIMDelivery
        fields = [
            "id",
            "esim",
            "delivery_method",
            "status",
            "recipient_email",
            "recipient_phone",
            "delivery_message",
            "sent_at",
            "delivered_at",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]

    def get_esim(self, obj):
        return ESIMSerializer(obj.esim).data


class ESIMDeliveryCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ESIM deliveries"""

    class Meta:
        model = ESIMDelivery
        fields = [
            "esim",
            "delivery_method",
            "recipient_email",
            "recipient_phone",
            "delivery_message",
        ]
