from rest_framework import serializers

from .models import Reseller, ResellerActivationRequest


class ResellerActivationRequestSerializer(serializers.ModelSerializer):
    """Reseller activation request serializer"""

    user = serializers.SerializerMethodField()

    class Meta:
        model = ResellerActivationRequest
        fields = [
            "id",
            "user",
            "max_clients",
            "max_sims",
            "credit_limit",
            "status",
            "admin_notes",
            "approved_by",
            "approved_at",
            "rejected_at",
            "created_at",
            "updated_at",
            "is_pending",
            "is_approved",
            "is_rejected",
        ]
        read_only_fields = [
            "id",
            "status",
            "admin_notes",
            "approved_by",
            "approved_at",
            "rejected_at",
            "created_at",
            "updated_at",
            "is_pending",
            "is_approved",
            "is_rejected",
        ]

    def get_user(self, obj):
        from accounts.serializers import UserSerializer

        return UserSerializer(obj.user).data


class ResellerActivationRequestCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating reseller activation requests"""

    user_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = ResellerActivationRequest
        fields = ["user_id", "max_clients", "max_sims", "credit_limit"]

    def validate_user_id(self, value):
        from accounts.models import User

        try:
            user = User.objects.get(id=value, role="reseller")
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found or not a reseller")
        return value

    def create(self, validated_data):
        user_id = validated_data.pop("user_id")
        from accounts.models import User

        user = User.objects.get(id=user_id)
        validated_data["user"] = user
        return super().create(validated_data)


class ResellerSerializer(serializers.ModelSerializer):
    """Reseller serializer"""

    user = serializers.SerializerMethodField()
    total_clients = serializers.SerializerMethodField()
    total_orders = serializers.SerializerMethodField()

    class Meta:
        model = Reseller
        fields = [
            "id",
            "user",
            "max_clients",
            "max_sims",
            "credit_limit",
            "current_credit",
            "is_suspended",
            "created_at",
            "updated_at",
            "total_clients",
            "total_orders",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_user(self, obj):
        from accounts.serializers import UserSerializer

        return UserSerializer(obj.user).data

    def get_total_clients(self, obj):
        return obj.clients.count()

    def get_total_orders(self, obj):
        return obj.orders.count()


class ResellerCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating resellers"""

    user_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = Reseller
        fields = ["user_id", "max_clients", "max_sims", "credit_limit"]

    def validate_user_id(self, value):
        from accounts.models import User

        try:
            user = User.objects.get(id=value, role="reseller")
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found or not a reseller")
        return value

    def create(self, validated_data):
        user_id = validated_data.pop("user_id")
        from accounts.models import User

        user = User.objects.get(id=user_id)
        validated_data["user"] = user
        return super().create(validated_data)


class ResellerUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating reseller data"""

    class Meta:
        model = Reseller
        fields = ["max_clients", "max_sims", "credit_limit"]


class ResellerDashboardSerializer(serializers.Serializer):
    """Reseller dashboard serializer"""

    reseller = ResellerSerializer()
    stats = serializers.DictField()
