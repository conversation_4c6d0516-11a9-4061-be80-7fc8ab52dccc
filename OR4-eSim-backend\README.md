# Or4 - eSIM Management System

A comprehensive Django-based eSIM management system with reseller management, client tracking, automated provisioning, and real-time analytics. Built with Django REST Framework, PostgreSQL, Redis, Celery, and Firebase Storage.

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- PostgreSQL 12+
- Redis 6+
- Firebase Project (for file storage)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Or4
   ```

2. **Create virtual environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp env.example .env
   # Edit .env with your configuration
   ```

5. **Set up PostgreSQL database**
   ```bash
   # Create database
   createdb your_database_name
   ```

6. **Run migrations**
   ```bash
   python manage.py migrate
   ```

7. **Create superuser**
   ```bash
   python manage.py createsuperuser
   ```

8. **Start Redis server**
   ```bash
   redis-server
   ```

9. **Start Celery worker (in new terminal)**
   ```bash
   celery -A esim_project worker -l info
   ```

10. **Start Celery beat scheduler (in new terminal)**
    ```bash
    celery -A esim_project beat -l info
    ```

11. **Run the development server**
    ```bash
    python manage.py runserver
    ```

## 🧪 Testing

Run the test suite:
```bash
python -m pytest
```

Run with coverage:
```bash
python -m pytest --cov=. --cov-report=html
```

Run specific test files:
```bash
python -m pytest tests/test_accounts_views.py
python -m pytest tests/test_api_views.py
```

## 🔧 Code Quality & Linting

This project uses automated code formatting and linting to maintain code quality.

### Setup (One-time)

```bash
# Install linting tools
pip install black isort flake8 pre-commit

# Run the setup script
./setup_linting.sh
```

### What's Included

- **Black**: Code formatter for consistent Python code style
- **isort**: Import sorting and organization
- **flake8**: Linting and style checking
- **pre-commit**: Git hooks for automatic formatting

### Usage

#### Manual Formatting
```bash
# Format all Python files
black .

# Sort imports
isort .

# Check linting
flake8
```

#### Automatic Formatting (Recommended)
The pre-commit hooks will automatically format your code when you commit:

```bash
# Make changes to your code
git add .
git commit -m "Your commit message"
# Code will be automatically formatted and linted
```

#### Run All Checks
```bash
pre-commit run --all-files
```

### Configuration Files

- **`.pre-commit-config.yaml`**: Pre-commit hook configuration
- **`pyproject.toml`**: Black, isort, and flake8 configuration
- **`.flake8`**: Additional flake8 configuration

### Workflow

1. **Make changes** to your code
2. **Stage changes**: `git add .`
3. **Commit**: `git commit -m "Your message"`
4. **Pre-commit hooks automatically**:
   - Format code with Black
   - Sort imports with isort
   - Check linting with flake8
   - If issues found, they're automatically fixed
   - If you need to commit again, the code will be properly formatted

### Skipping Hooks (Emergency Only)

If you need to skip the pre-commit hooks (not recommended):
```bash
git commit --no-verify -m "Emergency commit"
```

## 📁 Project Structure

```
Or4/
├── accounts/          # User authentication & profiles
├── api/              # API utilities & views
├── clients/          # Client management & support tickets
├── esim_management/  # eSIM provisioning & management
├── orders/           # Order processing & tracking
├── payments/         # Payment handling & gateways
├── reports/          # Analytics & reporting
├── resellers/        # Reseller management
├── tests/            # Test suite
├── esim_project/     # Django settings & configuration
├── logs/             # Application logs
├── static/           # Static files
└── venv/             # Virtual environment
```

## 🔐 Authentication & Authorization

The system supports multiple user roles with JWT-based authentication:

- **Admin**: Full system access and management
- **Reseller**: Manages clients, orders, and eSIM provisioning
- **Client**: Places orders and manages eSIMs
- **Public User**: Direct eSIM purchases

### JWT Configuration
- Access token lifetime: 60 minutes
- Refresh token lifetime: 7 days
- Secure cookie support for production

## 📊 API Endpoints

### API Documentation
- **Swagger UI**: `/swagger/`
- **ReDoc**: `/redoc/`
- **JSON Schema**: `/api-docs/`


## 🚀 Background Tasks & Scheduling

The system uses Celery for background task processing and scheduled operations:

### Scheduled Tasks
- **eSIM Status Updates**: Every 5 minutes
- **Daily Reports**: Every 24 hours
- **Session Cleanup**: Every hour
- **Password Reset Token Cleanup**: Every 2 hours
- **Inactive User Cleanup**: Every 7 days

### Task Management
```bash
# Start Celery worker
celery -A esim_project worker -l info

# Start Celery beat scheduler
celery -A esim_project beat -l info

# Monitor tasks
celery -A esim_project flower  # Optional: Web-based monitoring
```

## 🗄️ Database & Storage

### Database
- **Primary**: PostgreSQL 12+
- **Connection Pooling**: Configured for production scalability
- **Migrations**: Django ORM with version control

### File Storage
- **Development**: Local file system
- **Production**: Firebase Storage (configurable)
- **Alternatives**: AWS S3, DigitalOcean Spaces, Google Cloud Storage

### Caching
- **Redis**: Session storage, task queue, caching
- **Django Cache**: Framework-level caching

## 🔌 Third-Party Integrations

### Payment Gateways
- **Stripe**: Primary payment processor
- **Webhook Support**: Secure payment verification

### Communication
- **Email**: SMTP with TLS support
- **SMS**: External SMS gateway integration

### eSIM Providers
- **TraveRoam**: Primary eSIM provider
- **Webhook Integration**: Real-time status updates

## 🛠️ Development

### Adding New Features

1. **Create models** in the appropriate app
2. **Run migrations**: `python manage.py makemigrations && python manage.py migrate`
3. **Add serializers** for API endpoints
4. **Create views** with proper permissions
5. **Add tests** for new functionality
6. **Update documentation**

### Code Style

- Follow PEP 8 guidelines
- Use Black for formatting (88 character line length)
- Sort imports with isort
- Write docstrings for functions and classes
- Use type hints where appropriate

### Environment Configuration

The project uses `python-decouple` for environment variable management. Key configuration areas:

- **Database**: PostgreSQL connection settings
- **Security**: JWT and Django secret keys
- **External APIs**: TraveRoam, Stripe, Firebase
- **File Storage**: Firebase or alternative cloud storage
- **Email**: SMTP configuration
- **Redis**: Cache and task queue settings

## 🚀 Deployment

### Production Checklist

- [ ] Set `DEBUG = False` in settings
- [ ] Configure proper database settings with connection pooling
- [ ] Set up static file serving (nginx/Apache)
- [ ] Configure email settings with production SMTP
- [ ] Set up SSL certificates (Let's Encrypt)
- [ ] Configure Firebase Storage or alternative
- [ ] Set up Redis for production
- [ ] Configure Celery workers and beat scheduler
- [ ] Set up monitoring and logging
- [ ] Configure backup strategies
- [ ] Set up CI/CD pipeline

### Docker Support (Optional)

```bash
# Build and run with Docker Compose
docker-compose up -d

# Run specific services
docker-compose up -d postgres redis
docker-compose up -d web celery
```

### Environment Variables

Copy `env.example` to `.env` and configure:
- Database credentials
- JWT secrets
- External API keys
- File storage configuration
- Email settings
- Redis configuration

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `python -m pytest`
5. Format code: `black . && isort .`
6. Commit your changes (pre-commit hooks will run automatically)
7. Push to your fork
8. Create a pull request

### Development Guidelines

- Write comprehensive tests for new features
- Follow the established code style
- Update documentation for API changes
- Ensure all tests pass before submitting PR
- Use meaningful commit messages

## 📞 Support

For support and questions:
- **Documentation**: Check the API docs at `/swagger/`
- **Issues**: Report bugs via GitHub issues
- **Development**: Contact the development team

## 🔄 Changelog

### Recent Updates
- JWT-based authentication system
- Comprehensive API documentation with Swagger
- Background task processing with Celery
- Firebase Storage integration
- Support ticket system
- Real-time analytics and reporting
- Automated eSIM status updates
- Multi-role user management

---

**Built with ❤️ using Django, Django REST Framework, PostgreSQL, Redis, and Celery**
