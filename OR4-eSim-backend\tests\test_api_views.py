import pytest
from django.urls import reverse
from rest_framework import status

from accounts.models import User, UserProfile
from clients.models import Client
from esim_management.models import ESIM, ESIMPlan
from orders.models import Order
from payments.models import Payment
from resellers.models import Reseller


class TestDashboardView:
    """Test cases for dashboard view"""

    @pytest.mark.django_db
    def test_dashboard_admin_success(self, admin_client, user_factory):
        """Test admin dashboard with data"""
        client, admin_user = admin_client

        # Create some test data
        user1 = user_factory(role="public_user")
        user2 = user_factory(role="public_user")

        # Create reseller
        reseller = Reseller.objects.create(user=user_factory(role="reseller"))

        # Create client
        client_user = user_factory(role="client")
        client_obj = Client.objects.create(
            reseller=reseller,
            user=client_user,
            full_name="Test Client",
            email="<EMAIL>",
            phone_number="1234567890",
        )

        # Create order
        order = Order.objects.create(
            order_number="ORD-001",
            order_type="esim",
            order_source="reseller",
            client=client_obj,
            product_name="Test eSIM Plan",
            quantity=1,
            unit_price=100.00,
            subtotal=100.00,
            total_amount=100.00,
            status="completed",
        )

        # Create payment
        payment = Payment.objects.create(order=order, amount=100.00, status="completed")

        # Create eSIM plan first
        esim_plan = ESIMPlan.objects.create(
            name="Test Plan",
            description="Test eSIM Plan",
            country="Test Country",
            data_volume="1GB",
            validity_days=30,
            base_price=100.00,
            reseller_price=90.00,
            public_price=100.00,
            traveroam_plan_id="TEST-001",
        )

        # Create eSIM
        esim = ESIM.objects.create(
            plan=esim_plan, client=client_obj, status="activated"
        )

        url = reverse("dashboard")

        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert "data" in response.data

        # Check if dashboard data contains expected metrics
        dashboard_data = response.data["data"]
        assert "key_metrics" in dashboard_data
        assert "recent_activities" in dashboard_data
        assert "sales_trends" in dashboard_data
        assert "system_status" in dashboard_data

    @pytest.mark.django_db
    def test_dashboard_reseller_success(self, authenticated_client):
        """Test reseller dashboard"""
        client, user = authenticated_client
        user.role = "reseller"
        user.save()

        # Create reseller
        reseller = Reseller.objects.create(user=user)

        url = reverse("dashboard")

        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True

    @pytest.mark.django_db
    def test_dashboard_client_success(self, authenticated_client, user_factory):
        """Test client dashboard"""
        client, user = authenticated_client
        user.role = "client"
        user.save()

        # Create reseller
        reseller = Reseller.objects.create(user=user_factory(role="reseller"))

        # Create client
        client_obj = Client.objects.create(
            reseller=reseller,
            user=user,
            full_name=user.first_name + " " + user.last_name,
            email=user.email,
            phone_number="1234567890",
        )

        url = reverse("dashboard")

        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True

    @pytest.mark.django_db
    def test_dashboard_unauthorized(self, api_client):
        """Test dashboard without authentication"""
        url = reverse("dashboard")

        response = api_client.get(url)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestImageUploadViews:
    """Test cases for image upload views"""

    @pytest.mark.django_db
    def test_upload_profile_image_success(
        self, authenticated_client, test_image, mock_firebase_storage
    ):
        """Test successful profile image upload"""
        client, user = authenticated_client

        url = reverse("upload-profile-image")
        data = {"image": test_image}

        response = client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert "image_url" in response.data["data"]

    @pytest.mark.django_db
    def test_upload_profile_image_no_file(self, authenticated_client):
        """Test profile image upload without file"""
        client, user = authenticated_client

        url = reverse("upload-profile-image")
        data = {}

        response = client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data["success"] is False

    @pytest.mark.django_db
    def test_upload_profile_image_invalid_type(
        self, authenticated_client, mock_firebase_storage
    ):
        """Test profile image upload with invalid file type"""
        from django.core.files.uploadedfile import SimpleUploadedFile

        client, user = authenticated_client

        invalid_file = SimpleUploadedFile(
            name="test.txt", content=b"invalid content", content_type="text/plain"
        )

        url = reverse("upload-profile-image")
        data = {"image": invalid_file}

        response = client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid file type" in response.data["message"]

    @pytest.mark.django_db
    def test_upload_document_image_success(
        self, authenticated_client, test_image, mock_firebase_storage
    ):
        """Test successful document image upload"""
        client, user = authenticated_client

        url = reverse("upload-document-image")
        data = {"image": test_image, "document_type": "passport"}

        response = client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert "image_url" in response.data["data"]
        assert response.data["data"]["document_type"] == "passport"

    @pytest.mark.django_db
    def test_upload_document_image_missing_type(self, authenticated_client, test_image):
        """Test document image upload without document type"""
        client, user = authenticated_client

        url = reverse("upload-document-image")
        data = {"image": test_image}

        response = client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.data["success"] is False

    @pytest.mark.django_db
    def test_upload_document_image_unauthorized(self, api_client, test_image):
        """Test document image upload without authentication"""
        url = reverse("upload-document-image")
        data = {"image": test_image, "document_type": "passport"}

        response = api_client.post(url, data, format="multipart")

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.django_db
    def test_delete_profile_image_success(self, authenticated_client):
        """Test successful profile image deletion"""
        client, user = authenticated_client

        url = reverse("delete-profile-image")

        response = client.delete(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True


class TestTestView:
    """Test cases for test view"""

    @pytest.mark.django_db
    def test_test_view_success(self, api_client):
        """Test test view endpoint"""
        url = reverse("test_view")

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data["success"] is True
        assert response.data["data"]["message"] == "Hello World"
