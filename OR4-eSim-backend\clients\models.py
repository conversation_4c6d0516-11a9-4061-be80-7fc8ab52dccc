from django.conf import settings
from django.db import models
from simple_history.models import HistoricalRecords


class Client(models.Model):
    """
    Client model for reseller clients
    """

    reseller = models.ForeignKey(
        "resellers.Reseller", on_delete=models.CASCADE, related_name="clients"
    )
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="client_profile",
    )

    # Client information
    full_name = models.CharField(max_length=200)
    phone_number = models.CharField(max_length=15)
    passport_number = models.CharField(max_length=50, blank=True, null=True)
    national_id = models.CharField(max_length=50, blank=True, null=True)
    email = models.EmailField()
    country_of_travel = models.CharField(max_length=100, blank=True, null=True)
    date_of_travel = models.DateField(blank=True, null=True)

    # Status
    is_active = models.BooleanField(default=True)
    is_blocked = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # History tracking
    history = HistoricalRecords()

    class Meta:
        db_table = "clients"
        verbose_name = "Client"
        verbose_name_plural = "Clients"

    def __str__(self):
        return f"{self.full_name} ({self.reseller.user.email})"

    @property
    def total_esims(self):
        return self.esims.count()

    @property
    def active_esims(self):
        return self.esims.filter(status="active").count()


class PublicUser(models.Model):
    """
    Public user model for app-based SIM buyers
    """

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="public_user_profile",
    )

    # User information
    full_name = models.CharField(max_length=200)
    phone_number = models.CharField(max_length=15)
    email = models.EmailField()
    address = models.TextField()
    city = models.CharField(max_length=100)
    country = models.CharField(max_length=100)

    # Status
    is_active = models.BooleanField(default=True)
    is_blocked = models.BooleanField(default=False)

    # Preferences
    preferred_package = models.CharField(max_length=100, blank=True, null=True)
    preferred_network = models.CharField(max_length=100, blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # History tracking
    history = HistoricalRecords()

    class Meta:
        db_table = "public_users"
        verbose_name = "Public User"
        verbose_name_plural = "Public Users"

    def __str__(self):
        return f"{self.full_name} ({self.phone_number})"

    @property
    def total_orders(self):
        from orders.models import Order

        return Order.objects.filter(public_user=self).count()

    @property
    def total_spent(self):
        from orders.models import Order

        return (
            Order.objects.filter(
                public_user=self, status__in=["completed", "delivered"]
            ).aggregate(total=models.Sum("total_amount"))["total"]
            or 0
        )


class ClientActivity(models.Model):
    """
    Track client activities and actions
    """

    client = models.ForeignKey(
        Client, on_delete=models.CASCADE, related_name="activities"
    )
    action = models.CharField(max_length=100)
    description = models.TextField()
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "client_activities"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.client.full_name} - {self.action}"


class PublicUserActivity(models.Model):
    """
    Track public user activities and actions
    """

    public_user = models.ForeignKey(
        PublicUser, on_delete=models.CASCADE, related_name="activities"
    )
    action = models.CharField(max_length=100)
    description = models.TextField()
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "public_user_activities"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.public_user.full_name} - {self.action}"


class SupportTicket(models.Model):
    """
    Support tickets for users
    """

    class TicketStatus(models.TextChoices):
        OPEN = "open", "Open"
        IN_PROGRESS = "in_progress", "In Progress"
        RESOLVED = "resolved", "Resolved"
        CLOSED = "closed", "Closed"

    class TicketPriority(models.TextChoices):
        LOW = "low", "Low"
        MEDIUM = "medium", "Medium"
        HIGH = "high", "High"
        URGENT = "urgent", "Urgent"

    # User can be either client or public user
    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="support_tickets",
    )
    public_user = models.ForeignKey(
        PublicUser,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="support_tickets",
    )

    subject = models.CharField(max_length=200)
    description = models.TextField()
    status = models.CharField(
        max_length=20, choices=TicketStatus.choices, default=TicketStatus.OPEN
    )
    priority = models.CharField(
        max_length=20, choices=TicketPriority.choices, default=TicketPriority.MEDIUM
    )

    # Admin assignment
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assigned_tickets",
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "support_tickets"
        ordering = ["-created_at"]

    def __str__(self):
        user = self.client or self.public_user
        return f"Ticket #{self.id} - {user} - {self.subject}"

    @property
    def user(self):
        return self.client or self.public_user


class TicketResponse(models.Model):
    """
    Responses to support tickets
    """

    ticket = models.ForeignKey(
        SupportTicket, on_delete=models.CASCADE, related_name="responses"
    )
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    message = models.TextField()
    is_internal = models.BooleanField(
        default=False
    )  # Internal note vs customer response
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "ticket_responses"
        ordering = ["created_at"]

    def __str__(self):
        return f"Response to Ticket #{self.ticket.id} by {self.user.email}"
