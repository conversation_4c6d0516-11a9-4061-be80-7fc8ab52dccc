from django.db.models import Count, Q, Sum
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.utils import create_error_response, create_success_response

from .models import ESIM, ESIMDelivery, ESIMPlan, ESIMUsage, TraveRoamWebhook
from .serializers import (
    ESIMCreateSerializer,
    ESIMDeliveryCreateSerializer,
    ESIMDeliverySerializer,
    ESIMPlanCreateSerializer,
    ESIMPlanSerializer,
    ESIMSerializer,
    ESIMUsageCreateSerializer,
    ESIMUsageSerializer,
    TraveRoamWebhookSerializer,
)


class ESIMPlanViewSet(viewsets.ModelViewSet):
    """ESIM plan management API"""

    queryset = ESIMPlan.objects.all()
    serializer_class = ESIMPlanSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter plans based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return ESIMPlan.objects.none()

        if not self.request.user.is_authenticated:
            return ESIMPlan.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return ESIMPlan.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return ESIMPlan.objects.filter(is_active=True)
        elif hasattr(self.request.user, "is_client") and self.request.user.is_client:
            return ESIMPlan.objects.filter(is_active=True)
        elif (
            hasattr(self.request.user, "is_public_user")
            and self.request.user.is_public_user
        ):
            return ESIMPlan.objects.filter(is_active=True)
        return ESIMPlan.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return ESIMPlanCreateSerializer
        return ESIMPlanSerializer

    @action(detail=False, methods=["get"])
    def available_plans(self, request):
        """Get available ESIM plans"""
        plans = self.get_queryset().filter(is_active=True)
        serializer = self.get_serializer(plans, many=True)
        return create_success_response(
            "Available plans retrieved", data=serializer.data
        )


class ESIMViewSet(viewsets.ModelViewSet):
    """ESIM management API"""

    queryset = ESIM.objects.all()
    serializer_class = ESIMSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter ESIMs based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return ESIM.objects.none()

        if not self.request.user.is_authenticated:
            return ESIM.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return ESIM.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return ESIM.objects.filter(reseller__user=self.request.user)
        elif hasattr(self.request.user, "is_client") and self.request.user.is_client:
            return ESIM.objects.filter(client__user=self.request.user)
        elif (
            hasattr(self.request.user, "is_public_user")
            and self.request.user.is_public_user
        ):
            return ESIM.objects.filter(public_user__user=self.request.user)
        return ESIM.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return ESIMCreateSerializer
        return ESIMSerializer

    @action(detail=False, methods=["get"])
    def my_esims(self, request):
        """Get ESIMs for current user"""
        esims = self.get_queryset()
        serializer = self.get_serializer(esims, many=True)
        return create_success_response("ESIMs retrieved", data=serializer.data)

    @action(detail=True, methods=["post"])
    def activate_esim(self, request, pk=None):
        """Activate an ESIM"""
        try:
            esim = self.get_object()
            if esim.status == "inactive":
                esim.status = "active"
                esim.activated_at = timezone.now()
                esim.save()
                return create_success_response("ESIM activated successfully")
            else:
                return create_error_response(
                    "ESIM is already active", status_code=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return create_error_response(
                f"Failed to activate ESIM: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def deactivate_esim(self, request, pk=None):
        """Deactivate an ESIM"""
        try:
            esim = self.get_object()
            if esim.status == "active":
                esim.status = "inactive"
                esim.deactivated_at = timezone.now()
                esim.save()
                return create_success_response("ESIM deactivated successfully")
            else:
                return create_error_response(
                    "ESIM is not active", status_code=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return create_error_response(
                f"Failed to deactivate ESIM: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )


class ESIMUsageViewSet(viewsets.ModelViewSet):
    """ESIM usage management API"""

    queryset = ESIMUsage.objects.all()
    serializer_class = ESIMUsageSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter usage based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return ESIMUsage.objects.none()

        if not self.request.user.is_authenticated:
            return ESIMUsage.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return ESIMUsage.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return ESIMUsage.objects.filter(esim__reseller__user=self.request.user)
        elif hasattr(self.request.user, "is_client") and self.request.user.is_client:
            return ESIMUsage.objects.filter(esim__client__user=self.request.user)
        elif (
            hasattr(self.request.user, "is_public_user")
            and self.request.user.is_public_user
        ):
            return ESIMUsage.objects.filter(esim__public_user__user=self.request.user)
        return ESIMUsage.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return ESIMUsageCreateSerializer
        return ESIMUsageSerializer

    @action(detail=False, methods=["get"])
    def usage_statistics(self, request):
        """Get usage statistics"""
        queryset = self.get_queryset()

        # Monthly usage
        current_month = timezone.now().month
        monthly_usage = queryset.filter(created_at__month=current_month)

        stats = {
            "total_usage_records": queryset.count(),
            "monthly_usage_records": monthly_usage.count(),
            "total_data_used": queryset.aggregate(total=Sum("data_used"))["total"] or 0,
            "monthly_data_used": monthly_usage.aggregate(total=Sum("data_used"))[
                "total"
            ]
            or 0,
        }

        return create_success_response("Usage statistics retrieved", data=stats)


class TraveRoamWebhookViewSet(viewsets.ModelViewSet):
    """TraveRoam webhook management API"""

    queryset = TraveRoamWebhook.objects.all()
    serializer_class = TraveRoamWebhookSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Only admins can access webhooks"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return TraveRoamWebhook.objects.none()

        if not self.request.user.is_authenticated:
            return TraveRoamWebhook.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return TraveRoamWebhook.objects.all()
        return TraveRoamWebhook.objects.none()

    @action(detail=False, methods=["post"])
    def receive_webhook(self, request):
        """Receive webhook from TraveRoam"""
        try:
            # Process webhook data
            webhook_data = request.data
            webhook = TraveRoamWebhook.objects.create(
                event_type=webhook_data.get("event_type"),
                payload=webhook_data,
                processed=False,
            )

            # TODO: Process webhook based on event type
            # This would include updating ESIM status, usage, etc.

            return create_success_response("Webhook received successfully")
        except Exception as e:
            return create_error_response(
                f"Failed to process webhook: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )


class ESIMDeliveryViewSet(viewsets.ModelViewSet):
    """ESIM delivery management API"""

    queryset = ESIMDelivery.objects.all()
    serializer_class = ESIMDeliverySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter deliveries based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return ESIMDelivery.objects.none()

        if not self.request.user.is_authenticated:
            return ESIMDelivery.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return ESIMDelivery.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return ESIMDelivery.objects.filter(esim__reseller__user=self.request.user)
        elif hasattr(self.request.user, "is_client") and self.request.user.is_client:
            return ESIMDelivery.objects.filter(esim__client__user=self.request.user)
        elif (
            hasattr(self.request.user, "is_public_user")
            and self.request.user.is_public_user
        ):
            return ESIMDelivery.objects.filter(
                esim__public_user__user=self.request.user
            )
        return ESIMDelivery.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return ESIMDeliveryCreateSerializer
        return ESIMDeliverySerializer

    @action(detail=True, methods=["post"])
    def update_delivery_status(self, request, pk=None):
        """Update delivery status"""
        try:
            delivery = self.get_object()
            status = request.data.get("status")
            tracking_number = request.data.get("tracking_number", "")
            notes = request.data.get("notes", "")

            if status:
                delivery.status = status
                delivery.tracking_number = tracking_number
                delivery.notes = notes
                delivery.save()

                return create_success_response("Delivery status updated successfully")
            else:
                return create_error_response(
                    "Status is required", status_code=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return create_error_response(
                f"Failed to update delivery status: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )
