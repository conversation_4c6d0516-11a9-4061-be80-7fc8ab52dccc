from django.db.models import Count, Q, Sum
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.utils import create_error_response, create_success_response

from .models import (
    DeliveryTracking,
    Order,
    OrderItem,
    OrderNotification,
    OrderStatusHistory,
)
from .serializers import (
    DeliveryTrackingSerializer,
    OrderItemSerializer,
    OrderNotificationSerializer,
    OrderSerializer,
    OrderStatusHistorySerializer,
)


class OrderViewSet(viewsets.ModelViewSet):
    """Order management API"""

    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter orders based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return Order.objects.none()

        if self.request.user.is_admin:
            return Order.objects.all()
        elif self.request.user.is_reseller:
            return Order.objects.filter(reseller__user=self.request.user)
        elif self.request.user.is_client:
            return Order.objects.filter(client__user=self.request.user)
        elif self.request.user.is_public_user:
            return Order.objects.filter(public_user__user=self.request.user)
        return Order.objects.none()

    @action(detail=False, methods=["get"])
    def my_orders(self, request):
        """Get orders for current user"""
        orders = self.get_queryset()
        serializer = self.get_serializer(orders, many=True)
        return create_success_response("Orders retrieved", data=serializer.data)

    @action(detail=False, methods=["get"])
    def statistics(self, request):
        """Get order statistics"""
        queryset = self.get_queryset()

        # Monthly statistics
        current_month = timezone.now().month
        monthly_orders = queryset.filter(created_at__month=current_month)

        stats = {
            "total_orders": queryset.count(),
            "pending_orders": queryset.filter(status="pending").count(),
            "completed_orders": queryset.filter(status="completed").count(),
            "cancelled_orders": queryset.filter(status="cancelled").count(),
            "monthly_orders": monthly_orders.count(),
            "total_revenue": queryset.aggregate(total=Sum("total_amount"))["total"]
            or 0,
            "monthly_revenue": monthly_orders.aggregate(total=Sum("total_amount"))[
                "total"
            ]
            or 0,
        }

        return create_success_response("Order statistics retrieved", data=stats)

    @action(detail=True, methods=["post"])
    def cancel_order(self, request, pk=None):
        """Cancel an order"""
        try:
            order = self.get_object()
            if order.status in ["pending", "processing"]:
                order.status = "cancelled"
                order.save()

                # Create status history
                OrderStatusHistory.objects.create(
                    order=order, status="cancelled", notes="Order cancelled by user"
                )

                return create_success_response("Order cancelled successfully")
            else:
                return create_error_response(
                    "Order cannot be cancelled", status_code=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return create_error_response(
                f"Failed to cancel order: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def update_status(self, request, pk=None):
        """Update order status (admin only)"""
        try:
            order = self.get_object()
            new_status = request.data.get("status")
            notes = request.data.get("notes", "")

            if new_status:
                order.status = new_status
                order.save()

                # Create status history
                OrderStatusHistory.objects.create(
                    order=order, status=new_status, notes=notes
                )

                return create_success_response("Order status updated successfully")
            else:
                return create_error_response(
                    "Status is required", status_code=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return create_error_response(
                f"Failed to update status: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )


class OrderItemViewSet(viewsets.ModelViewSet):
    """Order item management API"""

    queryset = OrderItem.objects.all()
    serializer_class = OrderItemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter order items based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return OrderItem.objects.none()

        if self.request.user.is_admin:
            return OrderItem.objects.all()
        elif self.request.user.is_reseller:
            return OrderItem.objects.filter(order__reseller__user=self.request.user)
        elif self.request.user.is_client:
            return OrderItem.objects.filter(order__client__user=self.request.user)
        elif self.request.user.is_public_user:
            return OrderItem.objects.filter(order__public_user__user=self.request.user)
        return OrderItem.objects.none()


class OrderStatusHistoryViewSet(viewsets.ModelViewSet):
    """Order status history API"""

    queryset = OrderStatusHistory.objects.all()
    serializer_class = OrderStatusHistorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter status history based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return OrderStatusHistory.objects.none()

        if self.request.user.is_admin:
            return OrderStatusHistory.objects.all()
        elif self.request.user.is_reseller:
            return OrderStatusHistory.objects.filter(
                order__reseller__user=self.request.user
            )
        elif self.request.user.is_client:
            return OrderStatusHistory.objects.filter(
                order__client__user=self.request.user
            )
        elif self.request.user.is_public_user:
            return OrderStatusHistory.objects.filter(
                order__public_user__user=self.request.user
            )
        return OrderStatusHistory.objects.none()


class DeliveryTrackingViewSet(viewsets.ModelViewSet):
    """Delivery tracking API"""

    queryset = DeliveryTracking.objects.all()
    serializer_class = DeliveryTrackingSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter delivery tracking based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return DeliveryTracking.objects.none()

        if self.request.user.is_admin:
            return DeliveryTracking.objects.all()
        elif self.request.user.is_reseller:
            return DeliveryTracking.objects.filter(
                order__reseller__user=self.request.user
            )
        elif self.request.user.is_client:
            return DeliveryTracking.objects.filter(
                order__client__user=self.request.user
            )
        elif self.request.user.is_public_user:
            return DeliveryTracking.objects.filter(
                order__public_user__user=self.request.user
            )
        return DeliveryTracking.objects.none()

    @action(detail=True, methods=["post"])
    def update_tracking(self, request, pk=None):
        """Update delivery tracking status"""
        try:
            tracking = self.get_object()
            status = request.data.get("status")
            location = request.data.get("location", "")
            notes = request.data.get("notes", "")

            if status:
                tracking.status = status
                tracking.current_location = location
                tracking.notes = notes
                tracking.save()

                return create_success_response("Tracking updated successfully")
            else:
                return create_error_response(
                    "Status is required", status_code=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return create_error_response(
                f"Failed to update tracking: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )


class OrderNotificationViewSet(viewsets.ModelViewSet):
    """Order notification API"""

    queryset = OrderNotification.objects.all()
    serializer_class = OrderNotificationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter notifications based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return OrderNotification.objects.none()

        if self.request.user.is_admin:
            return OrderNotification.objects.all()
        elif self.request.user.is_reseller:
            return OrderNotification.objects.filter(
                order__reseller__user=self.request.user
            )
        elif self.request.user.is_client:
            return OrderNotification.objects.filter(
                order__client__user=self.request.user
            )
        elif self.request.user.is_public_user:
            return OrderNotification.objects.filter(
                order__public_user__user=self.request.user
            )
        return OrderNotification.objects.none()
