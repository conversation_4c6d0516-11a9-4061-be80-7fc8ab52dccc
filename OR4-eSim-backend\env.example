# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# JWT Settings
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ACCESS_TOKEN_LIFETIME=60  # minutes
JWT_REFRESH_TOKEN_LIFETIME=7  # days
JWT_COOKIE_SECURE=False  # Set to True in production with HTTPS
JWT_COOKIE_DOMAIN=None  # Set domain for production

# PostgreSQL Database Settings (Local Development)
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_HOST=localhost
DB_PORT=5432

# Production Database Settings (PostgreSQL)
# Uncomment and configure for production deployment
# DEBUG=False
# DB_NAME=your_production_database_name
# DB_USER=your_production_database_user
# DB_PASSWORD=your_production_database_password
# DB_HOST=your-db-host.com
# DB_PORT=5432

# Email Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Redis Settings
REDIS_URL=redis://localhost:6379/0

# TraveRoam API Settings
TRAVEROAM_API_KEY=your-traveroam-api-key
TRAVEROAM_API_URL=https://api.traveroam.com

# Firebase Configuration
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour Private Key Here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your-project.iam.gserviceaccount.com
FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com

# Payment Gateway Settings
STRIPE_PUBLIC_KEY=your-stripe-public-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# SMS Gateway Settings (if using external SMS service)
SMS_API_KEY=your-sms-api-key
SMS_API_URL=https://api.sms-gateway.com

# Production File Storage Settings (AWS S3)
# Configure these for production deployment
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=us-east-1

# Alternative: DigitalOcean Spaces
# AWS_ACCESS_KEY_ID=your-spaces-key
# AWS_SECRET_ACCESS_KEY=your-spaces-secret
# AWS_STORAGE_BUCKET_NAME=your-space-name
# AWS_S3_REGION_NAME=nyc3
# AWS_S3_ENDPOINT_URL=https://nyc3.digitaloceanspaces.com

# Alternative: Google Cloud Storage
# GS_BUCKET_NAME=your-gcs-bucket
# GS_CREDENTIALS=path/to/service-account-key.json 