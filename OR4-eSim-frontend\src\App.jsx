import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ThemeProvider } from './context/ThemeContext'
import { AuthProvider, useAuth } from './context/AuthContext'
import { Toaster } from 'react-hot-toast'

// Import the actual page components
import LoginPage from './pages/auth/LoginPage'
import SignupPage from './pages/auth/SignupPage'
import ForgotPasswordPage from './pages/auth/ForgotPasswordPage'
import ResetPasswordPage from './pages/auth/ResetPasswordPage'
import DashboardPage from './pages/dashboard/DashboardPage'
import ResellersPage from './pages/resellers/ResellersPage'
import UsersPage from './pages/users/UsersPage'
import OrdersPage from './pages/orders/OrdersPage'
import TransactionsPage from './pages/payments/TransactionsPage'
import ReportsPage from './pages/reports/ReportsPage'
import SettingsPage from './pages/settings/SettingsPage'
import DockSidebar from './components/common/DockSidebar/DockSidebar'

// Layout component for authenticated pages
function DashboardLayout({ children }) {
  const { logout } = useAuth()

  const handleLogout = () => {
    logout()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-between px-6 py-4">
          <h1 className="text-xl font-semibold text-gray-900">SIM Admin Panel</h1>
          <button
            onClick={handleLogout}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
          >
            Logout
          </button>
        </div>
      </header>

      {/* Content */}
      <main className="p-6 pb-20">
        {children}
      </main>

      {/* Dock Sidebar */}
      <DockSidebar />
    </div>
  )
}

// Protected Route Component
function ProtectedRoute({ children }) {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  return <DashboardLayout>{children}</DashboardLayout>
}

// Dashboard Page Wrapper
function DashboardPageWrapper() {
  return (
    <ProtectedRoute>
      <DashboardPage />
    </ProtectedRoute>
  )
}

// Resellers Page Wrapper
function ResellersPageWrapper() {
  return (
    <ProtectedRoute>
      <ResellersPage />
    </ProtectedRoute>
  )
}

// Users Page Wrapper
function UsersPageWrapper() {
  return (
    <ProtectedRoute>
      <UsersPage />
    </ProtectedRoute>
  )
}

// Orders Page Wrapper
function OrdersPageWrapper() {
  return (
    <ProtectedRoute>
      <OrdersPage />
    </ProtectedRoute>
  )
}

// Transactions Page Wrapper
function TransactionsPageWrapper() {
  return (
    <ProtectedRoute>
      <TransactionsPage />
    </ProtectedRoute>
  )
}

// Reports Page Wrapper
function ReportsPageWrapper() {
  return (
    <ProtectedRoute>
      <ReportsPage />
    </ProtectedRoute>
  )
}

// Settings Page Wrapper
function SettingsPageWrapper() {
  return (
    <ProtectedRoute>
      <SettingsPage />
    </ProtectedRoute>
  )
}

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <div className="min-h-screen">
            <Routes>
              <Route path="/login" element={<LoginPage />} />
              <Route path="/signup" element={<SignupPage />} />
              <Route path="/forgot-password" element={<ForgotPasswordPage />} />
              <Route path="/reset-password" element={<ResetPasswordPage />} />
              <Route path="/dashboard" element={<DashboardPageWrapper />} />
              <Route path="/resellers" element={<ResellersPageWrapper />} />
              <Route path="/users" element={<UsersPageWrapper />} />
              <Route path="/orders" element={<OrdersPageWrapper />} />
              <Route path="/transactions" element={<TransactionsPageWrapper />} />
              <Route path="/reports" element={<ReportsPageWrapper />} />
              <Route path="/settings" element={<SettingsPageWrapper />} />
              <Route path="/" element={<Navigate to="/login" replace />} />
              <Route path="*" element={<Navigate to="/login" replace />} />
            </Routes>
            <Toaster 
              position="top-right" 
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
                success: {
                  duration: 4000,
                  iconTheme: {
                    primary: '#10b981',
                    secondary: '#fff',
                  },
                },
                error: {
                  duration: 4000,
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#fff',
                  },
                },
              }}
            />
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  )
}

export default App
