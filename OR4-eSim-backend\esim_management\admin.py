from django.contrib import admin

from .models import ESIM, ESIMDelivery, ESIMPlan, ESIMUsage, TraveRoamWebhook


@admin.register(ESIMPlan)
class ESIMPlanAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "country",
        "data_volume",
        "validity_days",
        "base_price",
        "reseller_price",
        "public_price",
        "is_active",
    )
    list_filter = ("is_active", "plan_type", "country", "created_at")
    search_fields = ("name", "country", "description")
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        ("Plan Information", {"fields": ("name", "description", "country", "region")}),
        ("Data & Validity", {"fields": ("data_volume", "validity_days", "plan_type")}),
        ("Pricing", {"fields": ("base_price", "reseller_price", "public_price")}),
        ("TraveRoam Integration", {"fields": ("traveroam_plan_id", "is_active")}),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(ESIM)
class ESIMAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "get_user",
        "plan",
        "status",
        "data_used",
        "data_remaining",
        "created_at",
    )
    list_filter = ("status", "plan__country", "created_at", "assigned_at")
    search_fields = ("id", "client__full_name", "public_user__full_name", "plan__name")
    readonly_fields = (
        "created_at",
        "assigned_at",
        "activated_at",
        "expires_at",
        "updated_at",
    )
    raw_id_fields = ("plan", "client", "public_user", "reseller")

    fieldsets = (
        (
            "Basic Information",
            {"fields": ("plan", "client", "public_user", "reseller", "status")},
        ),
        ("eSIM Details", {"fields": ("qr_code", "activation_code")}),
        (
            "TraveRoam Integration",
            {"fields": ("traveroam_esim_id", "traveroam_order_id")},
        ),
        ("Usage Tracking", {"fields": ("data_used", "data_remaining")}),
        (
            "Timestamps",
            {
                "fields": (
                    "created_at",
                    "assigned_at",
                    "activated_at",
                    "expires_at",
                    "updated_at",
                ),
                "classes": ("collapse",),
            },
        ),
    )

    def get_user(self, obj):
        return obj.user

    get_user.short_description = "User"


@admin.register(ESIMUsage)
class ESIMUsageAdmin(admin.ModelAdmin):
    list_display = ("esim", "data_used", "location", "timestamp")
    list_filter = ("timestamp", "location")
    search_fields = ("esim__id", "location")
    readonly_fields = ("timestamp",)
    raw_id_fields = ("esim",)
    ordering = ("-timestamp",)


@admin.register(TraveRoamWebhook)
class TraveRoamWebhookAdmin(admin.ModelAdmin):
    list_display = ("id", "webhook_type", "esim", "processed", "created_at")
    list_filter = ("webhook_type", "processed", "created_at")
    search_fields = ("esim__id", "webhook_type")
    readonly_fields = ("created_at", "processed_at")
    raw_id_fields = ("esim",)
    ordering = ("-created_at",)

    fieldsets = (
        ("Webhook Information", {"fields": ("webhook_type", "esim", "payload")}),
        ("Processing", {"fields": ("processed", "processed_at")}),
        ("Timestamps", {"fields": ("created_at",), "classes": ("collapse",)}),
    )


@admin.register(ESIMDelivery)
class ESIMDeliveryAdmin(admin.ModelAdmin):
    list_display = ("esim", "delivery_method", "status", "recipient_email", "sent_at")
    list_filter = ("delivery_method", "status", "created_at")
    search_fields = ("esim__id", "recipient_email", "recipient_phone")
    readonly_fields = ("created_at", "sent_at", "delivered_at")
    raw_id_fields = ("esim",)

    fieldsets = (
        ("Delivery Information", {"fields": ("esim", "delivery_method", "status")}),
        (
            "Recipient Details",
            {"fields": ("recipient_email", "recipient_phone", "delivery_message")},
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "sent_at", "delivered_at"),
                "classes": ("collapse",),
            },
        ),
    )
