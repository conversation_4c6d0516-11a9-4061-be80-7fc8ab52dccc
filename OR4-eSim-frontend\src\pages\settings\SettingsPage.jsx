import { useState } from 'react'
import { useTheme } from '../../context/ThemeContext'
import toast from 'react-hot-toast'
import {
  Settings,
  User,
  DollarSign,
  Bell,
  Key,
  Palette,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  Upload,
  Download,
  Mail,
  MessageSquare,
  Smartphone,
  Globe,
  Shield,
  CreditCard,
  Truck,
  Calculator,
  Image,
  Code,
  Webhook,
  Lock,
  AlertCircle,
  CheckCircle,
  Copy,
  RotateCcw
} from 'lucide-react'

// Sample settings data
const initialSettings = {
  admin: {
    name: 'Admin User',
    email: '<EMAIL>',
    phone: '+971 50 123 4567',
    role: 'Super Admin',
    lastLogin: '2024-03-15T10:30:00Z',
    twoFactorEnabled: true
  },
  pricing: {
    deliveryFee: 15.00,
    taxRate: 5.0,
    serviceCharge: 2.50,
    currency: 'USD',
    freeDeliveryThreshold: 100.00,
    bulkDiscountThreshold: 10,
    bulkDiscountRate: 15.0
  },
  notifications: {
    sms: {
      orderConfirmation: 'Your eSIM order #{orderNumber} has been confirmed. We will notify you once it\'s ready for delivery.',
      orderDispatched: 'Great news! Your eSIM order #{orderNumber} has been dispatched. Tracking: {trackingNumber}',
      orderDelivered: 'Your eSIM order #{orderNumber} has been delivered successfully. Enjoy your new connection!',
      orderActivated: 'Your eSIM is now activated and ready to use. Welcome to our network!',
      paymentReceived: 'Payment of ${amount} received for order #{orderNumber}. Thank you!',
      refundProcessed: 'Your refund of ${amount} has been processed and will reflect in 3-5 business days.'
    },
    email: {
      orderConfirmation: {
        subject: 'Order Confirmation - #{orderNumber}',
        template: 'Dear {customerName},\n\nThank you for your order! Your eSIM order #{orderNumber} has been confirmed.\n\nOrder Details:\n- Plan: {planName}\n- Amount: ${amount}\n- Delivery Address: {address}\n\nWe will notify you once your order is ready for delivery.\n\nBest regards,\neSIM Platform Team'
      },
      orderDispatched: {
        subject: 'Order Dispatched - #{orderNumber}',
        template: 'Dear {customerName},\n\nGreat news! Your eSIM order #{orderNumber} has been dispatched.\n\nTracking Information:\n- Tracking Number: {trackingNumber}\n- Estimated Delivery: {estimatedDelivery}\n\nYou can track your order using the tracking number above.\n\nBest regards,\neSIM Platform Team'
      }
    }
  },
  api: {
    stripePublicKey: 'pk_test_51234567890abcdef',
    stripeSecretKey: 'sk_test_51234567890abcdef',
    paypalClientId: 'AX1234567890',
    paypalClientSecret: 'EX1234567890',
    smsApiKey: 'SMS_API_KEY_12345',
    emailApiKey: 'EMAIL_API_KEY_67890',
    webhookUrl: 'https://api.esim-platform.com/webhooks',
    webhookSecret: 'whsec_1234567890abcdef'
  },
  branding: {
    companyName: 'eSIM Platform',
    logoUrl: '/logo.png',
    primaryColor: '#3b82f6',
    secondaryColor: '#10b981',
    accentColor: '#f59e0b',
    supportEmail: '<EMAIL>',
    supportPhone: '+971 4 123 4567',
    website: 'https://esim-platform.com',
    address: 'Dubai, United Arab Emirates'
  }
}

function SettingsPage() {
  const { resolvedTheme } = useTheme()
  const [settings, setSettings] = useState(initialSettings)
  const [activeTab, setActiveTab] = useState('admin')
  const [showApiKeys, setShowApiKeys] = useState({})
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const tabs = [
    { id: 'admin', label: 'Admin Account', icon: User },
    { id: 'pricing', label: 'Pricing & Fees', icon: DollarSign },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'api', label: 'API & Webhooks', icon: Key },
    { id: 'branding', label: 'Branding', icon: Palette }
  ]

  const handleSettingChange = (section, field, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
    setHasUnsavedChanges(true)
  }

  const handleNestedSettingChange = (section, subsection, field, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...prev[section][subsection],
          [field]: value
        }
      }
    }))
    setHasUnsavedChanges(true)
  }

  const handleSaveSettings = () => {
    // Simulate API call
    toast.success('Settings saved successfully')
    setHasUnsavedChanges(false)
  }

  const handleResetSettings = () => {
    setSettings(initialSettings)
    setHasUnsavedChanges(false)
    toast.success('Settings reset to default values')
  }

  const toggleApiKeyVisibility = (keyName) => {
    setShowApiKeys(prev => ({
      ...prev,
      [keyName]: !prev[keyName]
    }))
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard')
  }

  const generateNewApiKey = (keyName) => {
    const newKey = `${keyName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    toast.success(`New ${keyName} generated`)
    return newKey
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Settings & Configurations</h1>
          <p className="text-muted-foreground">Manage system settings, pricing, and integrations</p>
        </div>
        <div className="flex items-center space-x-3">
          {hasUnsavedChanges && (
            <div className="flex items-center space-x-2 px-3 py-2 bg-yellow-50 dark:bg-yellow-500/10 rounded-lg">
              <AlertCircle className="h-4 w-4 text-yellow-500" />
              <span className="text-sm text-yellow-700 dark:text-yellow-400">Unsaved changes</span>
            </div>
          )}
          <button
            onClick={handleResetSettings}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              resolvedTheme === 'dark'
                ? 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
            }`}
          >
            <RotateCcw className="h-4 w-4" />
            <span>Reset</span>
          </button>
          <button
            onClick={handleSaveSettings}
            disabled={!hasUnsavedChanges}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              !hasUnsavedChanges
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            }`}
          >
            <Save className="h-4 w-4" />
            <span>Save Changes</span>
          </button>
        </div>
      </div>

      {/* Settings Container */}
      <div className="bg-card border border-border rounded-lg overflow-hidden">
        {/* Tabs */}
        <div className="border-b border-border">
          <div className="flex overflow-x-auto">
            {tabs.map((tab) => {
              const TabIcon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-6 py-4 border-b-2 transition-colors whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-primary text-primary bg-primary/5'
                      : 'border-transparent text-muted-foreground hover:text-foreground hover:bg-muted/50'
                  }`}
                >
                  <TabIcon className="h-4 w-4" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              )
            })}
          </div>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'admin' && (
            <div className="space-y-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className={`p-3 rounded-lg ${resolvedTheme === 'dark' ? 'bg-blue-500/10' : 'bg-blue-50'}`}>
                  <User className="h-6 w-6 text-blue-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">Admin Account Settings</h3>
                  <p className="text-sm text-muted-foreground">Manage your admin account and security settings</p>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Personal Information */}
                <div className="space-y-4">
                  <h4 className="font-medium text-foreground">Personal Information</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Full Name
                      </label>
                      <input
                        type="text"
                        value={settings.admin.name}
                        onChange={(e) => handleSettingChange('admin', 'name', e.target.value)}
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={settings.admin.email}
                        onChange={(e) => handleSettingChange('admin', 'email', e.target.value)}
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={settings.admin.phone}
                        onChange={(e) => handleSettingChange('admin', 'phone', e.target.value)}
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>

                {/* Security Settings */}
                <div className="space-y-4">
                  <h4 className="font-medium text-foreground">Security Settings</h4>
                  <div className="space-y-4">
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-foreground">Two-Factor Authentication</p>
                          <p className="text-sm text-muted-foreground">Add an extra layer of security</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.admin.twoFactorEnabled}
                            onChange={(e) => handleSettingChange('admin', 'twoFactorEnabled', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <button
                        className={`w-full flex items-center justify-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                          resolvedTheme === 'dark'
                            ? 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                            : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                        }`}
                      >
                        <Lock className="h-4 w-4" />
                        <span>Change Password</span>
                      </button>

                      <div className="p-3 bg-muted/50 rounded-lg">
                        <div className="flex items-center space-x-2 text-sm">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-muted-foreground">Last login:</span>
                          <span className="font-medium text-foreground">
                            {new Date(settings.admin.lastLogin).toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'pricing' && (
            <div className="space-y-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className={`p-3 rounded-lg ${resolvedTheme === 'dark' ? 'bg-green-500/10' : 'bg-green-50'}`}>
                  <DollarSign className="h-6 w-6 text-green-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">Pricing & Fees Configuration</h3>
                  <p className="text-sm text-muted-foreground">Set delivery fees, taxes, and service charges</p>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Basic Pricing */}
                <div className="space-y-4">
                  <h4 className="font-medium text-foreground">Basic Pricing</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Delivery Fee (${settings.pricing.currency})
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="number"
                          value={settings.pricing.deliveryFee}
                          onChange={(e) => handleSettingChange('pricing', 'deliveryFee', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                          className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Tax Rate (%)
                      </label>
                      <div className="relative">
                        <Calculator className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="number"
                          value={settings.pricing.taxRate}
                          onChange={(e) => handleSettingChange('pricing', 'taxRate', parseFloat(e.target.value) || 0)}
                          min="0"
                          max="100"
                          step="0.1"
                          className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Service Charge (${settings.pricing.currency})
                      </label>
                      <div className="relative">
                        <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="number"
                          value={settings.pricing.serviceCharge}
                          onChange={(e) => handleSettingChange('pricing', 'serviceCharge', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                          className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Advanced Pricing */}
                <div className="space-y-4">
                  <h4 className="font-medium text-foreground">Advanced Settings</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Free Delivery Threshold (${settings.pricing.currency})
                      </label>
                      <div className="relative">
                        <Truck className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="number"
                          value={settings.pricing.freeDeliveryThreshold}
                          onChange={(e) => handleSettingChange('pricing', 'freeDeliveryThreshold', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                          className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Bulk Discount Threshold (Units)
                      </label>
                      <input
                        type="number"
                        value={settings.pricing.bulkDiscountThreshold}
                        onChange={(e) => handleSettingChange('pricing', 'bulkDiscountThreshold', parseInt(e.target.value) || 0)}
                        min="1"
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Bulk Discount Rate (%)
                      </label>
                      <input
                        type="number"
                        value={settings.pricing.bulkDiscountRate}
                        onChange={(e) => handleSettingChange('pricing', 'bulkDiscountRate', parseFloat(e.target.value) || 0)}
                        min="0"
                        max="100"
                        step="0.1"
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className={`p-3 rounded-lg ${resolvedTheme === 'dark' ? 'bg-orange-500/10' : 'bg-orange-50'}`}>
                  <Bell className="h-6 w-6 text-orange-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">Notification Templates</h3>
                  <p className="text-sm text-muted-foreground">Manage SMS and email notification content</p>
                </div>
              </div>

              {/* SMS Templates */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="h-5 w-5 text-blue-500" />
                  <h4 className="font-medium text-foreground">SMS Templates</h4>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {Object.entries(settings.notifications.sms).map(([key, template]) => (
                    <div key={key} className="space-y-2">
                      <label className="block text-sm font-medium text-foreground">
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </label>
                      <textarea
                        value={template}
                        onChange={(e) => handleNestedSettingChange('notifications', 'sms', key, e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                      />
                      <p className="text-xs text-muted-foreground">
                        Available variables: {'{orderNumber}'}, {'{customerName}'}, {'{amount}'}, {'{trackingNumber}'}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Email Templates */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Mail className="h-5 w-5 text-green-500" />
                  <h4 className="font-medium text-foreground">Email Templates</h4>
                </div>
                <div className="space-y-6">
                  {Object.entries(settings.notifications.email).map(([key, template]) => (
                    <div key={key} className="p-4 border border-border rounded-lg space-y-4">
                      <h5 className="font-medium text-foreground">
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </h5>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">
                            Subject Line
                          </label>
                          <input
                            type="text"
                            value={template.subject}
                            onChange={(e) => handleNestedSettingChange('notifications', 'email', key, {
                              ...template,
                              subject: e.target.value
                            })}
                            className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">
                            Email Template
                          </label>
                          <textarea
                            value={template.template}
                            onChange={(e) => handleNestedSettingChange('notifications', 'email', key, {
                              ...template,
                              template: e.target.value
                            })}
                            rows={6}
                            className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                          />
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Available variables: {'{customerName}'}, {'{orderNumber}'}, {'{planName}'}, {'{amount}'}, {'{address}'}, {'{trackingNumber}'}, {'{estimatedDelivery}'}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'api' && (
            <div className="space-y-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className={`p-3 rounded-lg ${resolvedTheme === 'dark' ? 'bg-purple-500/10' : 'bg-purple-50'}`}>
                  <Key className="h-6 w-6 text-purple-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">API Keys & Webhooks</h3>
                  <p className="text-sm text-muted-foreground">Configure payment gateways and API integrations</p>
                </div>
              </div>

              {/* Payment Gateway APIs */}
              <div className="space-y-4">
                <h4 className="font-medium text-foreground">Payment Gateway Configuration</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h5 className="text-sm font-medium text-foreground">Stripe Configuration</h5>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Stripe Public Key
                        </label>
                        <div className="relative">
                          <input
                            type={showApiKeys.stripePublic ? 'text' : 'password'}
                            value={settings.api.stripePublicKey}
                            onChange={(e) => handleSettingChange('api', 'stripePublicKey', e.target.value)}
                            className="w-full px-3 py-2 pr-20 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                            <button
                              type="button"
                              onClick={() => toggleApiKeyVisibility('stripePublic')}
                              className="p-1 text-muted-foreground hover:text-foreground"
                            >
                              {showApiKeys.stripePublic ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </button>
                            <button
                              type="button"
                              onClick={() => copyToClipboard(settings.api.stripePublicKey)}
                              className="p-1 text-muted-foreground hover:text-foreground"
                            >
                              <Copy className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Stripe Secret Key
                        </label>
                        <div className="relative">
                          <input
                            type={showApiKeys.stripeSecret ? 'text' : 'password'}
                            value={settings.api.stripeSecretKey}
                            onChange={(e) => handleSettingChange('api', 'stripeSecretKey', e.target.value)}
                            className="w-full px-3 py-2 pr-20 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                            <button
                              type="button"
                              onClick={() => toggleApiKeyVisibility('stripeSecret')}
                              className="p-1 text-muted-foreground hover:text-foreground"
                            >
                              {showApiKeys.stripeSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </button>
                            <button
                              type="button"
                              onClick={() => copyToClipboard(settings.api.stripeSecretKey)}
                              className="p-1 text-muted-foreground hover:text-foreground"
                            >
                              <Copy className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h5 className="text-sm font-medium text-foreground">PayPal Configuration</h5>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          PayPal Client ID
                        </label>
                        <div className="relative">
                          <input
                            type={showApiKeys.paypalClient ? 'text' : 'password'}
                            value={settings.api.paypalClientId}
                            onChange={(e) => handleSettingChange('api', 'paypalClientId', e.target.value)}
                            className="w-full px-3 py-2 pr-20 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                            <button
                              type="button"
                              onClick={() => toggleApiKeyVisibility('paypalClient')}
                              className="p-1 text-muted-foreground hover:text-foreground"
                            >
                              {showApiKeys.paypalClient ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </button>
                            <button
                              type="button"
                              onClick={() => copyToClipboard(settings.api.paypalClientId)}
                              className="p-1 text-muted-foreground hover:text-foreground"
                            >
                              <Copy className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          PayPal Client Secret
                        </label>
                        <div className="relative">
                          <input
                            type={showApiKeys.paypalSecret ? 'text' : 'password'}
                            value={settings.api.paypalClientSecret}
                            onChange={(e) => handleSettingChange('api', 'paypalClientSecret', e.target.value)}
                            className="w-full px-3 py-2 pr-20 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                            <button
                              type="button"
                              onClick={() => toggleApiKeyVisibility('paypalSecret')}
                              className="p-1 text-muted-foreground hover:text-foreground"
                            >
                              {showApiKeys.paypalSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </button>
                            <button
                              type="button"
                              onClick={() => copyToClipboard(settings.api.paypalClientSecret)}
                              className="p-1 text-muted-foreground hover:text-foreground"
                            >
                              <Copy className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Communication APIs */}
              <div className="space-y-4">
                <h4 className="font-medium text-foreground">Communication APIs</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      SMS API Key
                    </label>
                    <div className="relative">
                      <input
                        type={showApiKeys.smsApi ? 'text' : 'password'}
                        value={settings.api.smsApiKey}
                        onChange={(e) => handleSettingChange('api', 'smsApiKey', e.target.value)}
                        className="w-full px-3 py-2 pr-20 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                        <button
                          type="button"
                          onClick={() => toggleApiKeyVisibility('smsApi')}
                          className="p-1 text-muted-foreground hover:text-foreground"
                        >
                          {showApiKeys.smsApi ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                        <button
                          type="button"
                          onClick={() => copyToClipboard(settings.api.smsApiKey)}
                          className="p-1 text-muted-foreground hover:text-foreground"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Email API Key
                    </label>
                    <div className="relative">
                      <input
                        type={showApiKeys.emailApi ? 'text' : 'password'}
                        value={settings.api.emailApiKey}
                        onChange={(e) => handleSettingChange('api', 'emailApiKey', e.target.value)}
                        className="w-full px-3 py-2 pr-20 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                        <button
                          type="button"
                          onClick={() => toggleApiKeyVisibility('emailApi')}
                          className="p-1 text-muted-foreground hover:text-foreground"
                        >
                          {showApiKeys.emailApi ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                        <button
                          type="button"
                          onClick={() => copyToClipboard(settings.api.emailApiKey)}
                          className="p-1 text-muted-foreground hover:text-foreground"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Webhook Configuration */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Webhook className="h-5 w-5 text-purple-500" />
                  <h4 className="font-medium text-foreground">Webhook Configuration</h4>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Webhook URL
                    </label>
                    <input
                      type="url"
                      value={settings.api.webhookUrl}
                      onChange={(e) => handleSettingChange('api', 'webhookUrl', e.target.value)}
                      className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Webhook Secret
                    </label>
                    <div className="relative">
                      <input
                        type={showApiKeys.webhookSecret ? 'text' : 'password'}
                        value={settings.api.webhookSecret}
                        onChange={(e) => handleSettingChange('api', 'webhookSecret', e.target.value)}
                        className="w-full px-3 py-2 pr-20 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                        <button
                          type="button"
                          onClick={() => toggleApiKeyVisibility('webhookSecret')}
                          className="p-1 text-muted-foreground hover:text-foreground"
                        >
                          {showApiKeys.webhookSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                        <button
                          type="button"
                          onClick={() => copyToClipboard(settings.api.webhookSecret)}
                          className="p-1 text-muted-foreground hover:text-foreground"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* API Security Notice */}
              <div className="p-4 bg-red-50 dark:bg-red-500/10 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Shield className="h-5 w-5 text-red-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-red-700 dark:text-red-400">Security Notice</h4>
                    <p className="text-sm text-red-600 dark:text-red-300 mt-1">
                      Keep your API keys secure and never share them publicly.
                      Regenerate keys immediately if you suspect they have been compromised.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'branding' && (
            <div className="space-y-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className={`p-3 rounded-lg ${resolvedTheme === 'dark' ? 'bg-pink-500/10' : 'bg-pink-50'}`}>
                  <Palette className="h-6 w-6 text-pink-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">Branding & Logo Setup</h3>
                  <p className="text-sm text-muted-foreground">Customize your platform's appearance and branding</p>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Company Information */}
                <div className="space-y-4">
                  <h4 className="font-medium text-foreground">Company Information</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Company Name
                      </label>
                      <input
                        type="text"
                        value={settings.branding.companyName}
                        onChange={(e) => handleSettingChange('branding', 'companyName', e.target.value)}
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Support Email
                      </label>
                      <input
                        type="email"
                        value={settings.branding.supportEmail}
                        onChange={(e) => handleSettingChange('branding', 'supportEmail', e.target.value)}
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Support Phone
                      </label>
                      <input
                        type="tel"
                        value={settings.branding.supportPhone}
                        onChange={(e) => handleSettingChange('branding', 'supportPhone', e.target.value)}
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Website URL
                      </label>
                      <input
                        type="url"
                        value={settings.branding.website}
                        onChange={(e) => handleSettingChange('branding', 'website', e.target.value)}
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Company Address
                      </label>
                      <textarea
                        value={settings.branding.address}
                        onChange={(e) => handleSettingChange('branding', 'address', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                      />
                    </div>
                  </div>
                </div>

                {/* Visual Branding */}
                <div className="space-y-4">
                  <h4 className="font-medium text-foreground">Visual Branding</h4>
                  <div className="space-y-4">
                    {/* Logo Upload */}
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Company Logo
                      </label>
                      <div className="space-y-3">
                        <div className={`border-2 border-dashed border-border rounded-lg p-6 text-center ${
                          resolvedTheme === 'dark' ? 'hover:border-slate-500' : 'hover:border-gray-400'
                        } transition-colors cursor-pointer`}>
                          <Image className="mx-auto h-12 w-12 text-muted-foreground" />
                          <div className="mt-2">
                            <p className="text-sm font-medium text-foreground">Upload Logo</p>
                            <p className="text-xs text-muted-foreground">PNG, JPG up to 2MB</p>
                          </div>
                        </div>
                        <input
                          type="url"
                          value={settings.branding.logoUrl}
                          onChange={(e) => handleSettingChange('branding', 'logoUrl', e.target.value)}
                          placeholder="Or enter logo URL"
                          className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>

                    {/* Color Scheme */}
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Primary Color
                        </label>
                        <div className="flex items-center space-x-3">
                          <input
                            type="color"
                            value={settings.branding.primaryColor}
                            onChange={(e) => handleSettingChange('branding', 'primaryColor', e.target.value)}
                            className="w-12 h-10 border border-border rounded-lg cursor-pointer"
                          />
                          <input
                            type="text"
                            value={settings.branding.primaryColor}
                            onChange={(e) => handleSettingChange('branding', 'primaryColor', e.target.value)}
                            className="flex-1 px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Secondary Color
                        </label>
                        <div className="flex items-center space-x-3">
                          <input
                            type="color"
                            value={settings.branding.secondaryColor}
                            onChange={(e) => handleSettingChange('branding', 'secondaryColor', e.target.value)}
                            className="w-12 h-10 border border-border rounded-lg cursor-pointer"
                          />
                          <input
                            type="text"
                            value={settings.branding.secondaryColor}
                            onChange={(e) => handleSettingChange('branding', 'secondaryColor', e.target.value)}
                            className="flex-1 px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Accent Color
                        </label>
                        <div className="flex items-center space-x-3">
                          <input
                            type="color"
                            value={settings.branding.accentColor}
                            onChange={(e) => handleSettingChange('branding', 'accentColor', e.target.value)}
                            className="w-12 h-10 border border-border rounded-lg cursor-pointer"
                          />
                          <input
                            type="text"
                            value={settings.branding.accentColor}
                            onChange={(e) => handleSettingChange('branding', 'accentColor', e.target.value)}
                            className="flex-1 px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Branding Preview */}
                    <div className="p-4 bg-muted/50 rounded-lg">
                      <h5 className="font-medium text-foreground mb-3">Preview</h5>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-4 h-4 rounded"
                            style={{ backgroundColor: settings.branding.primaryColor }}
                          ></div>
                          <span className="text-sm text-foreground">Primary Color</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-4 h-4 rounded"
                            style={{ backgroundColor: settings.branding.secondaryColor }}
                          ></div>
                          <span className="text-sm text-foreground">Secondary Color</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-4 h-4 rounded"
                            style={{ backgroundColor: settings.branding.accentColor }}
                          ></div>
                          <span className="text-sm text-foreground">Accent Color</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default SettingsPage

          {activeTab === 'pricing' && (
            <div className="space-y-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className={`p-3 rounded-lg ${resolvedTheme === 'dark' ? 'bg-green-500/10' : 'bg-green-50'}`}>
                  <DollarSign className="h-6 w-6 text-green-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">Pricing & Fees Configuration</h3>
                  <p className="text-sm text-muted-foreground">Set delivery fees, taxes, and service charges</p>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Basic Pricing */}
                <div className="space-y-4">
                  <h4 className="font-medium text-foreground">Basic Pricing</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Delivery Fee (${settings.pricing.currency})
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="number"
                          value={settings.pricing.deliveryFee}
                          onChange={(e) => handleSettingChange('pricing', 'deliveryFee', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                          className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Tax Rate (%)
                      </label>
                      <div className="relative">
                        <Calculator className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="number"
                          value={settings.pricing.taxRate}
                          onChange={(e) => handleSettingChange('pricing', 'taxRate', parseFloat(e.target.value) || 0)}
                          min="0"
                          max="100"
                          step="0.1"
                          className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Service Charge (${settings.pricing.currency})
                      </label>
                      <div className="relative">
                        <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="number"
                          value={settings.pricing.serviceCharge}
                          onChange={(e) => handleSettingChange('pricing', 'serviceCharge', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                          className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Currency
                      </label>
                      <select
                        value={settings.pricing.currency}
                        onChange={(e) => handleSettingChange('pricing', 'currency', e.target.value)}
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      >
                        <option value="USD">USD - US Dollar</option>
                        <option value="AED">AED - UAE Dirham</option>
                        <option value="EUR">EUR - Euro</option>
                        <option value="GBP">GBP - British Pound</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Advanced Pricing */}
                <div className="space-y-4">
                  <h4 className="font-medium text-foreground">Advanced Pricing</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Free Delivery Threshold (${settings.pricing.currency})
                      </label>
                      <div className="relative">
                        <Truck className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="number"
                          value={settings.pricing.freeDeliveryThreshold}
                          onChange={(e) => handleSettingChange('pricing', 'freeDeliveryThreshold', parseFloat(e.target.value) || 0)}
                          min="0"
                          step="0.01"
                          className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Orders above this amount get free delivery
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Bulk Discount Threshold (Units)
                      </label>
                      <input
                        type="number"
                        value={settings.pricing.bulkDiscountThreshold}
                        onChange={(e) => handleSettingChange('pricing', 'bulkDiscountThreshold', parseInt(e.target.value) || 0)}
                        min="1"
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Minimum units for bulk discount
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Bulk Discount Rate (%)
                      </label>
                      <input
                        type="number"
                        value={settings.pricing.bulkDiscountRate}
                        onChange={(e) => handleSettingChange('pricing', 'bulkDiscountRate', parseFloat(e.target.value) || 0)}
                        min="0"
                        max="100"
                        step="0.1"
                        className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Discount percentage for bulk orders
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Pricing Preview */}
              <div className="p-4 bg-muted/50 rounded-lg">
                <h4 className="font-medium text-foreground mb-3">Pricing Preview</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="space-y-2">
                    <p className="text-muted-foreground">Sample Order: $50</p>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>$50.00</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Delivery:</span>
                        <span>${settings.pricing.deliveryFee.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Service Charge:</span>
                        <span>${settings.pricing.serviceCharge.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax ({settings.pricing.taxRate}%):</span>
                        <span>${((50 + settings.pricing.deliveryFee + settings.pricing.serviceCharge) * settings.pricing.taxRate / 100).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between font-medium border-t border-border pt-1">
                        <span>Total:</span>
                        <span>${(50 + settings.pricing.deliveryFee + settings.pricing.serviceCharge + ((50 + settings.pricing.deliveryFee + settings.pricing.serviceCharge) * settings.pricing.taxRate / 100)).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-muted-foreground">Free Delivery: $150</p>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>$150.00</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Delivery:</span>
                        <span className="text-green-500">FREE</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Service Charge:</span>
                        <span>${settings.pricing.serviceCharge.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax ({settings.pricing.taxRate}%):</span>
                        <span>${((150 + settings.pricing.serviceCharge) * settings.pricing.taxRate / 100).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between font-medium border-t border-border pt-1">
                        <span>Total:</span>
                        <span>${(150 + settings.pricing.serviceCharge + ((150 + settings.pricing.serviceCharge) * settings.pricing.taxRate / 100)).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-muted-foreground">Bulk Order: 15 units</p>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>$750.00</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Bulk Discount ({settings.pricing.bulkDiscountRate}%):</span>
                        <span className="text-green-500">-${(750 * settings.pricing.bulkDiscountRate / 100).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Delivery:</span>
                        <span className="text-green-500">FREE</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Service Charge:</span>
                        <span>${settings.pricing.serviceCharge.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between font-medium border-t border-border pt-1">
                        <span>Total:</span>
                        <span>${(750 - (750 * settings.pricing.bulkDiscountRate / 100) + settings.pricing.serviceCharge).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
