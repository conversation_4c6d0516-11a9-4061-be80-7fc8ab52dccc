from django.contrib import admin

from .models import (
    Client,
    ClientActivity,
    PublicUser,
    PublicUserActivity,
    SupportTicket,
    TicketResponse,
)


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = (
        "full_name",
        "reseller",
        "phone_number",
        "email",
        "is_active",
        "is_blocked",
        "total_esims",
    )
    list_filter = ("is_active", "is_blocked", "created_at", "country_of_travel")
    search_fields = (
        "full_name",
        "phone_number",
        "email",
        "passport_number",
        "national_id",
    )
    readonly_fields = ("total_esims", "active_esims")
    raw_id_fields = ("reseller", "user")

    fieldsets = (
        (
            "Basic Information",
            {"fields": ("reseller", "user", "full_name", "phone_number", "email")},
        ),
        (
            "Travel Information",
            {
                "fields": (
                    "passport_number",
                    "national_id",
                    "country_of_travel",
                    "date_of_travel",
                )
            },
        ),
        ("Status", {"fields": ("is_active", "is_blocked")}),
        (
            "Statistics",
            {"fields": ("total_esims", "active_esims"), "classes": ("collapse",)},
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(PublicUser)
class PublicUserAdmin(admin.ModelAdmin):
    list_display = (
        "full_name",
        "phone_number",
        "email",
        "city",
        "is_active",
        "is_blocked",
        "total_orders",
    )
    list_filter = ("is_active", "is_blocked", "created_at", "city", "country")
    search_fields = ("full_name", "phone_number", "email", "address")
    readonly_fields = ("total_orders", "total_spent")
    raw_id_fields = ("user",)

    fieldsets = (
        (
            "Basic Information",
            {"fields": ("user", "full_name", "phone_number", "email")},
        ),
        ("Address", {"fields": ("address", "city", "country")}),
        ("Preferences", {"fields": ("preferred_package", "preferred_network")}),
        ("Status", {"fields": ("is_active", "is_blocked")}),
        (
            "Statistics",
            {"fields": ("total_orders", "total_spent"), "classes": ("collapse",)},
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(ClientActivity)
class ClientActivityAdmin(admin.ModelAdmin):
    list_display = ("client", "action", "ip_address", "created_at")
    list_filter = ("action", "created_at")
    search_fields = ("client__full_name", "action", "description")
    readonly_fields = ("created_at",)
    ordering = ("-created_at",)


@admin.register(PublicUserActivity)
class PublicUserActivityAdmin(admin.ModelAdmin):
    list_display = ("public_user", "action", "ip_address", "created_at")
    list_filter = ("action", "created_at")
    search_fields = ("public_user__full_name", "action", "description")
    readonly_fields = ("created_at",)
    ordering = ("-created_at",)


class TicketResponseInline(admin.TabularInline):
    model = TicketResponse
    extra = 0
    readonly_fields = ("created_at",)


@admin.register(SupportTicket)
class SupportTicketAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "get_user",
        "subject",
        "status",
        "priority",
        "assigned_to",
        "created_at",
    )
    list_filter = ("status", "priority", "created_at")
    search_fields = (
        "subject",
        "description",
        "client__full_name",
        "public_user__full_name",
    )
    readonly_fields = ("created_at", "updated_at")
    raw_id_fields = ("client", "public_user", "assigned_to")
    inlines = [TicketResponseInline]

    fieldsets = (
        (
            "Ticket Information",
            {"fields": ("client", "public_user", "subject", "description")},
        ),
        ("Status", {"fields": ("status", "priority", "assigned_to")}),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "resolved_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def get_user(self, obj):
        return obj.user

    get_user.short_description = "User"


@admin.register(TicketResponse)
class TicketResponseAdmin(admin.ModelAdmin):
    list_display = ("ticket", "user", "is_internal", "created_at")
    list_filter = ("is_internal", "created_at")
    search_fields = ("ticket__subject", "user__email", "message")
    readonly_fields = ("created_at",)
    raw_id_fields = ("ticket", "user")
