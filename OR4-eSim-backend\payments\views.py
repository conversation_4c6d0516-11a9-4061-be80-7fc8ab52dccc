from django.db.models import Count, Q, Sum
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.utils import create_error_response, create_success_response

from .models import Payment
from .serializers import (
    PaymentCreateSerializer,
    PaymentDashboardSerializer,
    PaymentSerializer,
    PaymentUpdateSerializer,
    RefundCreateSerializer,
)


class PaymentViewSet(viewsets.ModelViewSet):
    """Payment management API"""

    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter payments based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return Payment.objects.none()

        if not self.request.user.is_authenticated:
            return Payment.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return Payment.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return Payment.objects.filter(order__reseller__user=self.request.user)
        elif hasattr(self.request.user, "is_client") and self.request.user.is_client:
            return Payment.objects.filter(order__client__user=self.request.user)
        elif (
            hasattr(self.request.user, "is_public_user")
            and self.request.user.is_public_user
        ):
            return Payment.objects.filter(order__public_user__user=self.request.user)
        return Payment.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return PaymentCreateSerializer
        elif self.action in ["update", "partial_update"]:
            return PaymentUpdateSerializer
        return PaymentSerializer

    @action(detail=False, methods=["post"])
    def make_payment(self, request):
        """Create a new payment"""
        try:
            serializer = PaymentCreateSerializer(data=request.data)
            if serializer.is_valid():
                payment = serializer.save()

                # TODO: Integrate with actual payment gateway
                # This is a placeholder for payment processing logic

                return create_success_response(
                    "Payment created successfully", data=PaymentSerializer(payment).data
                )
            else:
                return create_error_response(
                    "Invalid payment data",
                    errors=serializer.errors,
                    status_code=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            return create_error_response(
                f"Failed to create payment: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def process_payment(self, request, pk=None):
        """Process a payment"""
        try:
            payment = self.get_object()

            # TODO: Integrate with actual payment gateway
            # This is a placeholder for payment processing logic

            payment.status = "completed"
            payment.processed_at = timezone.now()
            payment.completed_at = timezone.now()
            payment.save()

            return create_success_response("Payment processed successfully")
        except Exception as e:
            return create_error_response(
                f"Failed to process payment: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def create_refund(self, request, pk=None):
        """Create a refund for a payment"""
        try:
            payment = self.get_object()

            if payment.status == "refunded":
                return create_error_response(
                    "Payment already refunded", status_code=status.HTTP_400_BAD_REQUEST
                )

            serializer = RefundCreateSerializer(
                payment, data=request.data, partial=True
            )
            if serializer.is_valid():
                # Set payment status to refunded
                payment.status = "refunded"
                payment.refund_approved_by = request.user
                payment.refund_approved_at = timezone.now()
                payment.save()

                return create_success_response("Refund created successfully")
            else:
                return create_error_response(
                    "Invalid refund data",
                    errors=serializer.errors,
                    status_code=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            return create_error_response(
                f"Failed to create refund: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def generate_invoice(self, request, pk=None):
        """Generate an invoice for a payment (uses payment data)"""
        try:
            payment = self.get_object()

            # TODO: Generate invoice using payment data
            # This would typically generate a PDF or return invoice data
            # using the payment information (amount, currency, order details, etc.)

            invoice_data = {
                "payment_id": payment.id,
                "transaction_id": payment.transaction_id,
                "amount": payment.amount,
                "currency": payment.currency,
                "payment_method": payment.payment_method,
                "order_number": payment.order.order_number,
                "customer_name": (
                    payment.order.customer.full_name
                    if payment.order.customer
                    else "Unknown"
                ),
                "created_at": payment.created_at,
                "status": payment.status,
            }

            return create_success_response(
                "Invoice generated successfully", data=invoice_data
            )
        except Exception as e:
            return create_error_response(
                f"Failed to generate invoice: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["get"])
    def payment_statistics(self, request):
        """Get payment statistics"""
        queryset = self.get_queryset()

        # Monthly statistics
        current_month = timezone.now().month
        monthly_payments = queryset.filter(created_at__month=current_month)

        stats = {
            "total_payments": queryset.count(),
            "monthly_payments": monthly_payments.count(),
            "total_amount": queryset.aggregate(total=Sum("amount"))["total"] or 0,
            "monthly_amount": monthly_payments.aggregate(total=Sum("amount"))["total"]
            or 0,
            "completed_payments": queryset.filter(status="completed").count(),
            "pending_payments": queryset.filter(status="pending").count(),
            "failed_payments": queryset.filter(status="failed").count(),
            "refunded_payments": queryset.filter(status="refunded").count(),
        }

        return create_success_response("Payment statistics retrieved", data=stats)

    @action(detail=False, methods=["get"])
    def my_payments(self, request):
        """Get payments for current user"""
        payments = self.get_queryset().order_by("-created_at")
        serializer = self.get_serializer(payments, many=True)
        return create_success_response("Payments retrieved", data=serializer.data)
