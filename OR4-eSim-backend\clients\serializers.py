from rest_framework import serializers

from .models import Client, PublicUser, SupportTicket, TicketResponse


class ClientSerializer(serializers.ModelSerializer):
    """Client serializer"""

    user = serializers.SerializerMethodField()
    reseller = serializers.SerializerMethodField()
    total_orders = serializers.SerializerMethodField()

    class Meta:
        model = Client
        fields = [
            "id",
            "user",
            "reseller",
            "full_name",
            "email",
            "phone_number",
            "passport_number",
            "national_id",
            "country_of_travel",
            "date_of_travel",
            "is_active",
            "is_blocked",
            "created_at",
            "updated_at",
            "total_orders",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_user(self, obj):
        from accounts.serializers import UserSerializer

        return UserSerializer(obj.user).data

    def get_reseller(self, obj):
        from resellers.serializers import ResellerSerializer

        return ResellerSerializer(obj.reseller).data

    def get_total_orders(self, obj):
        return obj.orders.count()


class ClientCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating clients"""

    user_id = serializers.IntegerField(write_only=True)
    reseller_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = Client
        fields = [
            "user_id",
            "reseller_id",
            "full_name",
            "email",
            "phone_number",
            "passport_number",
            "national_id",
            "country_of_travel",
            "date_of_travel",
        ]

    def validate_user_id(self, value):
        from accounts.models import User

        try:
            user = User.objects.get(id=value, role="client")
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found or not a client")
        return value

    def validate_reseller_id(self, value):
        from resellers.models import Reseller

        try:
            reseller = Reseller.objects.get(id=value)
        except Reseller.DoesNotExist:
            raise serializers.ValidationError("Reseller not found")
        return value

    def create(self, validated_data):
        user_id = validated_data.pop("user_id")
        reseller_id = validated_data.pop("reseller_id")

        from accounts.models import User
        from resellers.models import Reseller

        user = User.objects.get(id=user_id)
        reseller = Reseller.objects.get(id=reseller_id)

        validated_data["user"] = user
        validated_data["reseller"] = reseller
        return super().create(validated_data)


class ClientUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating client data"""

    class Meta:
        model = Client
        fields = [
            "full_name",
            "email",
            "phone_number",
            "passport_number",
            "national_id",
            "country_of_travel",
            "date_of_travel",
        ]


class PublicUserSerializer(serializers.ModelSerializer):
    """Public user serializer"""

    user = serializers.SerializerMethodField()
    total_orders = serializers.SerializerMethodField()

    class Meta:
        model = PublicUser
        fields = [
            "id",
            "user",
            "full_name",
            "phone_number",
            "email",
            "address",
            "city",
            "country",
            "is_active",
            "is_blocked",
            "preferred_package",
            "preferred_network",
            "created_at",
            "updated_at",
            "total_orders",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_user(self, obj):
        from accounts.serializers import UserSerializer

        return UserSerializer(obj.user).data

    def get_total_orders(self, obj):
        return obj.orders.count()


class PublicUserCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating public users"""

    user_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = PublicUser
        fields = [
            "user_id",
            "full_name",
            "phone_number",
            "email",
            "address",
            "city",
            "country",
            "preferred_package",
            "preferred_network",
        ]

    def validate_user_id(self, value):
        from accounts.models import User

        try:
            user = User.objects.get(id=value, role="public_user")
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found or not a public user")
        return value

    def create(self, validated_data):
        user_id = validated_data.pop("user_id")
        from accounts.models import User

        user = User.objects.get(id=user_id)
        validated_data["user"] = user
        return super().create(validated_data)


class PublicUserUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating public user data"""

    class Meta:
        model = PublicUser
        fields = [
            "full_name",
            "phone_number",
            "email",
            "address",
            "city",
            "country",
            "preferred_package",
            "preferred_network",
        ]


class SupportTicketSerializer(serializers.ModelSerializer):
    """Support ticket serializer"""

    client = serializers.SerializerMethodField()
    public_user = serializers.SerializerMethodField()
    responses = serializers.SerializerMethodField()

    class Meta:
        model = SupportTicket
        fields = [
            "id",
            "client",
            "public_user",
            "subject",
            "description",
            "priority",
            "status",
            "assigned_to",
            "created_at",
            "updated_at",
            "resolved_at",
            "responses",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "resolved_at"]

    def get_client(self, obj):
        if obj.client:
            return ClientSerializer(obj.client).data
        return None

    def get_public_user(self, obj):
        if obj.public_user:
            return PublicUserSerializer(obj.public_user).data
        return None

    def get_responses(self, obj):
        responses = obj.ticket_responses.all().order_by("created_at")
        return TicketResponseSerializer(responses, many=True).data


class SupportTicketCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating support tickets"""

    class Meta:
        model = SupportTicket
        fields = ["subject", "description", "priority"]

    def create(self, validated_data):
        request = self.context["request"]
        validated_data["client"] = getattr(request.user, "client", None)
        validated_data["public_user"] = getattr(request.user, "public_user", None)
        return super().create(validated_data)


class TicketResponseSerializer(serializers.ModelSerializer):
    """Ticket response serializer"""

    ticket = serializers.SerializerMethodField()
    user = serializers.SerializerMethodField()

    class Meta:
        model = TicketResponse
        fields = ["id", "ticket", "user", "message", "is_internal", "created_at"]
        read_only_fields = ["id", "created_at"]

    def get_ticket(self, obj):
        return SupportTicketSerializer(obj.ticket).data

    def get_user(self, obj):
        from accounts.serializers import UserSerializer

        return UserSerializer(obj.user).data


class TicketResponseCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ticket responses"""

    class Meta:
        model = TicketResponse
        fields = ["message", "is_internal"]

    def create(self, validated_data):
        request = self.context["request"]
        validated_data["user"] = request.user
        validated_data["ticket"] = self.context["ticket"]
        return super().create(validated_data)
