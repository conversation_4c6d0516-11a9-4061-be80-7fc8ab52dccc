from django.contrib import admin

from .models import (
    DeliveryTracking,
    Order,
    OrderItem,
    OrderNotification,
    OrderStatusHistory,
)


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ("created_at",)


class OrderStatusHistoryInline(admin.TabularInline):
    model = OrderStatusHistory
    extra = 0
    readonly_fields = ("created_at",)


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = (
        "order_number",
        "order_type",
        "order_source",
        "status",
        "customer",
        "total_amount",
        "created_at",
    )
    list_filter = ("order_type", "order_source", "status", "created_at")
    search_fields = (
        "order_number",
        "product_name",
        "customer__full_name",
        "customer__phone_number",
    )
    readonly_fields = (
        "order_number",
        "created_at",
        "updated_at",
        "confirmed_at",
        "dispatched_at",
        "delivered_at",
        "completed_at",
        "cancelled_at",
    )
    raw_id_fields = ("public_user", "reseller", "client")
    inlines = [OrderItemInline, OrderStatusHistoryInline]

    fieldsets = (
        (
            "Order Information",
            {"fields": ("order_number", "order_type", "order_source", "status")},
        ),
        ("Customer Information", {"fields": ("public_user", "reseller", "client")}),
        (
            "Product Details",
            {"fields": ("product_name", "product_description", "quantity")},
        ),
        (
            "Pricing",
            {
                "fields": (
                    "unit_price",
                    "subtotal",
                    "tax_amount",
                    "delivery_fee",
                    "total_amount",
                )
            },
        ),
        (
            "Delivery Information",
            {
                "fields": (
                    "delivery_address",
                    "delivery_city",
                    "delivery_country",
                    "delivery_phone",
                )
            },
        ),
        (
            "Timestamps",
            {
                "fields": (
                    "created_at",
                    "updated_at",
                    "confirmed_at",
                    "dispatched_at",
                    "delivered_at",
                    "completed_at",
                    "cancelled_at",
                ),
                "classes": ("collapse",),
            },
        ),
    )

    def customer(self, obj):
        return obj.customer

    customer.short_description = "Customer"


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    list_display = ("order", "product_name", "quantity", "unit_price", "total_price")
    list_filter = ("created_at",)
    search_fields = ("order__order_number", "product_name")
    readonly_fields = ("created_at",)
    raw_id_fields = ("order", "esim")


@admin.register(OrderStatusHistory)
class OrderStatusHistoryAdmin(admin.ModelAdmin):
    list_display = ("order", "old_status", "new_status", "changed_by", "created_at")
    list_filter = ("old_status", "new_status", "created_at")
    search_fields = ("order__order_number", "changed_by__email")
    readonly_fields = ("created_at",)
    raw_id_fields = ("order", "changed_by")
    ordering = ("-created_at",)


@admin.register(DeliveryTracking)
class DeliveryTrackingAdmin(admin.ModelAdmin):
    list_display = (
        "order",
        "tracking_number",
        "courier_name",
        "status",
        "estimated_delivery",
    )
    list_filter = ("status", "created_at")
    search_fields = ("order__order_number", "tracking_number", "courier_name")
    readonly_fields = ("created_at", "updated_at")
    raw_id_fields = ("order",)

    fieldsets = (
        (
            "Tracking Information",
            {"fields": ("order", "tracking_number", "courier_name", "status")},
        ),
        (
            "Location & Delivery",
            {"fields": ("current_location", "estimated_delivery", "actual_delivery")},
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(OrderNotification)
class OrderNotificationAdmin(admin.ModelAdmin):
    list_display = (
        "order",
        "notification_type",
        "notification_method",
        "recipient",
        "delivered",
        "sent_at",
    )
    list_filter = ("notification_type", "notification_method", "delivered", "sent_at")
    search_fields = ("order__order_number", "recipient", "message")
    readonly_fields = ("sent_at", "delivered_at")
    raw_id_fields = ("order",)
    ordering = ("-sent_at",)
