from django.contrib import admin

from .models import Payment


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = (
        "transaction_id",
        "order",
        "amount",
        "currency",
        "payment_method",
        "status",
        "refund_amount",
        "created_at",
    )
    list_filter = ("status", "created_at")
    search_fields = ("transaction_id", "order__order_number", "gateway_transaction_id")
    readonly_fields = ("created_at", "updated_at", "processed_at", "completed_at")

    fieldsets = (
        (
            "Payment Information",
            {"fields": ("order", "amount", "currency", "payment_method", "status")},
        ),
        (
            "Transaction Details",
            {
                "fields": (
                    "transaction_id",
                    "gateway_transaction_id",
                    "gateway_response",
                )
            },
        ),
        (
            "Refund Information",
            {
                "fields": (
                    "refund_amount",
                    "refund_reason",
                    "refund_approved_by",
                    "refund_approved_at",
                ),
                "classes": ("collapse",),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "processed_at", "completed_at"),
                "classes": ("collapse",),
            },
        ),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("order")
