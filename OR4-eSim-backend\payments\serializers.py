from rest_framework import serializers

from .models import Payment


class PaymentSerializer(serializers.ModelSerializer):
    """Payment serializer"""

    order = serializers.SerializerMethodField()

    class Meta:
        model = Payment
        fields = [
            "id",
            "order",
            "amount",
            "currency",
            "payment_method",
            "status",
            "transaction_id",
            "gateway_transaction_id",
            "gateway_response",
            "refund_amount",
            "refund_reason",
            "refund_approved_by",
            "refund_approved_at",
            "created_at",
            "updated_at",
            "processed_at",
            "completed_at",
            "is_refunded",
            "net_amount",
        ]
        read_only_fields = [
            "id",
            "created_at",
            "updated_at",
            "is_refunded",
            "net_amount",
        ]

    def get_order(self, obj):
        from orders.serializers import OrderSerializer

        return OrderSerializer(obj.order).data


class PaymentCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating payments"""

    class Meta:
        model = Payment
        fields = [
            "order",
            "amount",
            "currency",
            "payment_method",
            "transaction_id",
            "gateway_transaction_id",
            "gateway_response",
        ]


class PaymentUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating payments"""

    class Meta:
        model = Payment
        fields = [
            "status",
            "gateway_transaction_id",
            "gateway_response",
            "processed_at",
            "completed_at",
        ]


class RefundCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating refunds"""

    class Meta:
        model = Payment
        fields = ["refund_amount", "refund_reason"]

    def validate_refund_amount(self, value):
        if value > self.instance.amount:
            raise serializers.ValidationError(
                "Refund amount cannot exceed payment amount"
            )
        return value


class PaymentDashboardSerializer(serializers.Serializer):
    """Payment dashboard serializer"""

    payment = PaymentSerializer()
    stats = serializers.DictField()
