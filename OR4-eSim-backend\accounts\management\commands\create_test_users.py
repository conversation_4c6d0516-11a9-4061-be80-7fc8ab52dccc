from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

from clients.models import Client, PublicUser
from resellers.models import Reseller

User = get_user_model()


class Command(BaseCommand):
    help = "Create test users for API testing"

    def handle(self, *args, **options):
        self.stdout.write("Creating test users...")

        # Create admin user
        admin_user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "first_name": "Admin",
                "last_name": "User",
                "role": "admin",
                "is_staff": True,
                "is_superuser": True,
            },
        )
        if created:
            admin_user.set_password("admin123")
            admin_user.save()
            self.stdout.write(
                self.style.SUCCESS("✅ Admin user created: admin/admin123")
            )
        else:
            self.stdout.write("ℹ️ Admin user already exists")

        # Create reseller user
        reseller_user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "first_name": "Reseller",
                "last_name": "User",
                "role": "reseller",
            },
        )
        if created:
            reseller_user.set_password("reseller123")
            reseller_user.save()

            # Create reseller profile
            reseller, reseller_created = Reseller.objects.get_or_create(
                user=reseller_user,
                defaults={
                    "max_clients": 50,
                    "max_sims": 500,
                    "credit_limit": 5000.00,
                },
            )
            if reseller_created:
                self.stdout.write(
                    self.style.SUCCESS("✅ Reseller user created: reseller/reseller123")
                )
            else:
                self.stdout.write("ℹ️ Reseller profile already exists")
        else:
            self.stdout.write("ℹ️ Reseller user already exists")

        # Create client user
        client_user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "first_name": "Client",
                "last_name": "User",
                "role": "client",
            },
        )
        if created:
            client_user.set_password("client123")
            client_user.save()

            # Create client profile
            client, client_created = Client.objects.get_or_create(
                user=client_user,
                defaults={
                    "reseller": reseller,
                    "full_name": "Test Client",
                    "phone_number": "+1234567890",
                    "passport_number": "PASS123456",
                    "national_id": "NAT123456",
                    "email": "<EMAIL>",
                    "country_of_travel": "USA",
                },
            )
            if client_created:
                self.stdout.write(
                    self.style.SUCCESS("✅ Client user created: client/client123")
                )
            else:
                self.stdout.write("ℹ️ Client profile already exists")
        else:
            self.stdout.write("ℹ️ Client user already exists")

        # Create public user
        public_user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "first_name": "Public",
                "last_name": "User",
                "role": "public_user",
            },
        )
        if created:
            public_user.set_password("public123")
            public_user.save()

            # Create public user profile
            public_profile, profile_created = PublicUser.objects.get_or_create(
                user=public_user,
                defaults={
                    "full_name": "Test Public User",
                    "phone_number": "+1234567890",
                    "email": "<EMAIL>",
                    "address": "456 Public St, City, Country",
                    "city": "Test City",
                    "country": "Test Country",
                },
            )
            if profile_created:
                self.stdout.write(
                    self.style.SUCCESS("✅ Public user created: public/public123")
                )
            else:
                self.stdout.write("ℹ️ Public user profile already exists")
        else:
            self.stdout.write("ℹ️ Public user already exists")

        self.stdout.write(self.style.SUCCESS("\n🎉 Test users created successfully!"))
        self.stdout.write("\nTest Credentials:")
        self.stdout.write("Admin: admin/admin123")
        self.stdout.write("Reseller: reseller/reseller123")
        self.stdout.write("Client: client/client123")
        self.stdout.write("Public: public/public123")
