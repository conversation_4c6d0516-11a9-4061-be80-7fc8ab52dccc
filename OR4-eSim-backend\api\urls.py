from django.urls import include, path
from rest_framework.routers import DefaultRouter

# Import views from each app
from accounts.views import (
    UserProfileViewSet,
    UserViewSet,
    edit_profile_view,
    login_view,
    logout_view,
    password_change_view,
    password_reset_confirm_view,
    password_reset_request_view,
    refresh_token_view,
    signup_view,
    user_profile_view,
    verify_token_view,
)
from clients.views import (
    ClientViewSet,
    PublicUserViewSet,
    SupportTicketViewSet,
    TicketResponseViewSet,
)
from esim_management.views import (
    ESIMDeliveryViewSet,
    ESIMPlanViewSet,
    ESIMUsageViewSet,
    ESIMViewSet,
    TraveRoamWebhookViewSet,
)
from orders.views import (
    DeliveryTrackingViewSet,
    OrderItemViewSet,
    OrderNotificationViewSet,
    OrderStatusHistoryViewSet,
    OrderViewSet,
)
from payments.views import PaymentViewSet
from reports.views import (
    AnalyticsEventViewSet,
    PerformanceMetricViewSet,
    ReportScheduleViewSet,
    ReportViewSet,
)
from resellers.views import ResellerActivationRequestViewSet, ResellerViewSet

from . import views

# Create routers for each app
accounts_router = DefaultRouter()
accounts_router.register(r"users", UserViewSet)
accounts_router.register(r"user-profiles", UserProfileViewSet)

resellers_router = DefaultRouter()
resellers_router.register(r"resellers", ResellerViewSet)
resellers_router.register(
    r"reseller-activation-requests", ResellerActivationRequestViewSet
)

clients_router = DefaultRouter()
clients_router.register(r"clients", ClientViewSet)
clients_router.register(r"public-users", PublicUserViewSet)
clients_router.register(r"support-tickets", SupportTicketViewSet)
clients_router.register(r"ticket-responses", TicketResponseViewSet)

esim_router = DefaultRouter()
esim_router.register(r"esim-plans", ESIMPlanViewSet)
esim_router.register(r"esims", ESIMViewSet)
esim_router.register(r"esim-usage", ESIMUsageViewSet)
esim_router.register(r"traveroam-webhooks", TraveRoamWebhookViewSet)
esim_router.register(r"esim-deliveries", ESIMDeliveryViewSet)

orders_router = DefaultRouter()
orders_router.register(r"orders", OrderViewSet)
orders_router.register(r"order-items", OrderItemViewSet)
orders_router.register(r"order-status-history", OrderStatusHistoryViewSet)
orders_router.register(r"delivery-tracking", DeliveryTrackingViewSet)
orders_router.register(r"order-notifications", OrderNotificationViewSet)

payments_router = DefaultRouter()
payments_router.register(r"payments", PaymentViewSet)

reports_router = DefaultRouter()
reports_router.register(r"reports", ReportViewSet)
reports_router.register(r"analytics-events", AnalyticsEventViewSet)
reports_router.register(r"performance-metrics", PerformanceMetricViewSet)
reports_router.register(r"report-schedules", ReportScheduleViewSet)


urlpatterns = [
    # JWT Authentication endpoints
    path("auth/signup/", signup_view, name="signup"),
    path("auth/login/", login_view, name="login"),
    path("auth/logout/", logout_view, name="logout"),
    path("auth/refresh/", refresh_token_view, name="refresh-token"),
    path("auth/verify/", verify_token_view, name="verify_token"),
    # Password management endpoints
    path(
        "auth/password-reset-request/",
        password_reset_request_view,
        name="password-reset-request",
    ),
    path(
        "auth/password-reset-confirm/",
        password_reset_confirm_view,
        name="password-reset-confirm",
    ),
    path("auth/password-change/", password_change_view, name="password-change"),
    path("auth/profile/", user_profile_view, name="user-profile"),
    path("auth/edit-profile/<str:email>/", edit_profile_view, name="edit-profile"),
    # Image upload endpoints
    path(
        "upload/profile-image/", views.upload_profile_image, name="upload-profile-image"
    ),
    path(
        "upload/document-image/",
        views.upload_document_image,
        name="upload-document-image",
    ),
    path(
        "upload/delete-profile-image/",
        views.delete_profile_image,
        name="delete-profile-image",
    ),
    # Dashboard endpoint
    path("dashboard/", views.dashboard_view, name="dashboard"),
    # Test endpoint
    path("test/", views.test_view, name="test_view"),
    # App-specific API routers
    path("accounts/", include(accounts_router.urls)),
    path("resellers/", include(resellers_router.urls)),
    path("clients/", include(clients_router.urls)),
    path("esim/", include(esim_router.urls)),
    path("orders/", include(orders_router.urls)),
    path("payments/", include(payments_router.urls)),
    path("reports/", include(reports_router.urls)),
]
