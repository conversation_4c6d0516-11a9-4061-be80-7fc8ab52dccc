from django.db.models import Count, Q, Sum
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.utils import create_error_response, create_success_response

from .models import Client, PublicUser, SupportTicket, TicketResponse
from .serializers import (
    ClientCreateSerializer,
    ClientSerializer,
    ClientUpdateSerializer,
    PublicUserCreateSerializer,
    PublicUserSerializer,
    PublicUserUpdateSerializer,
    SupportTicketCreateSerializer,
    SupportTicketSerializer,
    TicketResponseCreateSerializer,
    TicketResponseSerializer,
)


class ClientViewSet(viewsets.ModelViewSet):
    """Client management API"""

    queryset = Client.objects.all()
    serializer_class = ClientSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter clients based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return Client.objects.none()

        if not self.request.user.is_authenticated:
            return Client.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return Client.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return Client.objects.filter(reseller__user=self.request.user)
        elif hasattr(self.request.user, "is_client") and self.request.user.is_client:
            return Client.objects.filter(user=self.request.user)
        return Client.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return ClientCreateSerializer
        elif self.action in ["update", "partial_update"]:
            return ClientUpdateSerializer
        return ClientSerializer

    @action(detail=False, methods=["get"])
    def my_clients(self, request):
        """Get clients for current reseller"""
        clients = self.get_queryset()
        serializer = self.get_serializer(clients, many=True)
        return create_success_response("Clients retrieved", data=serializer.data)

    @action(detail=False, methods=["get"])
    def statistics(self, request):
        """Get client statistics"""
        queryset = self.get_queryset()

        stats = {
            "total_clients": queryset.count(),
            "active_clients": queryset.filter(is_active=True).count(),
            "new_clients_this_month": queryset.filter(
                created_at__month=timezone.now().month
            ).count(),
        }

        return create_success_response("Client statistics retrieved", data=stats)


class PublicUserViewSet(viewsets.ModelViewSet):
    """Public user management API"""

    queryset = PublicUser.objects.all()
    serializer_class = PublicUserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter public users based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return PublicUser.objects.none()

        if not self.request.user.is_authenticated:
            return PublicUser.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return PublicUser.objects.all()
        elif (
            hasattr(self.request.user, "is_public_user")
            and self.request.user.is_public_user
        ):
            return PublicUser.objects.filter(user=self.request.user)
        return PublicUser.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return PublicUserCreateSerializer
        elif self.action in ["update", "partial_update"]:
            return PublicUserUpdateSerializer
        return PublicUserSerializer

    @action(detail=False, methods=["get"])
    def my_profile(self, request):
        """Get current user's profile"""
        try:
            profile = PublicUser.objects.get(user=request.user)
            serializer = self.get_serializer(profile)
            return create_success_response("Profile retrieved", data=serializer.data)
        except PublicUser.DoesNotExist:
            return create_error_response(
                "Profile not found", status_code=status.HTTP_404_NOT_FOUND
            )


class SupportTicketViewSet(viewsets.ModelViewSet):
    """Support ticket management API"""

    queryset = SupportTicket.objects.all()
    serializer_class = SupportTicketSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter tickets based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return SupportTicket.objects.none()

        if not self.request.user.is_authenticated:
            return SupportTicket.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return SupportTicket.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return SupportTicket.objects.filter(
                client__reseller__user=self.request.user
            )
        elif hasattr(self.request.user, "is_client") and self.request.user.is_client:
            return SupportTicket.objects.filter(client__user=self.request.user)
        elif (
            hasattr(self.request.user, "is_public_user")
            and self.request.user.is_public_user
        ):
            return SupportTicket.objects.filter(public_user__user=self.request.user)
        return SupportTicket.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return SupportTicketCreateSerializer
        return SupportTicketSerializer

    @action(detail=False, methods=["get"])
    def my_tickets(self, request):
        """Get tickets for current user"""
        tickets = self.get_queryset()
        serializer = self.get_serializer(tickets, many=True)
        return create_success_response("Tickets retrieved", data=serializer.data)

    @action(detail=True, methods=["post"])
    def close_ticket(self, request, pk=None):
        """Close a support ticket"""
        try:
            ticket = self.get_object()
            ticket.status = "closed"
            ticket.closed_at = timezone.now()
            ticket.save()
            return create_success_response("Ticket closed successfully")
        except Exception as e:
            return create_error_response(
                f"Failed to close ticket: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )


class TicketResponseViewSet(viewsets.ModelViewSet):
    """Ticket response management API"""

    queryset = TicketResponse.objects.all()
    serializer_class = TicketResponseSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter responses based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return TicketResponse.objects.none()

        if not self.request.user.is_authenticated:
            return TicketResponse.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return TicketResponse.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return TicketResponse.objects.filter(
                ticket__client__reseller__user=self.request.user
            )
        elif hasattr(self.request.user, "is_client") and self.request.user.is_client:
            return TicketResponse.objects.filter(ticket__client__user=self.request.user)
        elif (
            hasattr(self.request.user, "is_public_user")
            and self.request.user.is_public_user
        ):
            return TicketResponse.objects.filter(
                ticket__public_user__user=self.request.user
            )
        return TicketResponse.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return TicketResponseCreateSerializer
        return TicketResponseSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if self.action == "create":
            context["ticket"] = self.request.data.get("ticket")
        return context
