from datetime import timed<PERSON><PERSON>

from django.db.models import Avg, Count, Q, Sum
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.utils import create_error_response, create_success_response

from .models import AnalyticsEvent, PerformanceMetric, Report, ReportSchedule
from .serializers import (
    AnalyticsEventCreateSerializer,
    AnalyticsEventSerializer,
    PerformanceMetricSerializer,
    ReportCreateSerializer,
    ReportScheduleCreateSerializer,
    ReportScheduleSerializer,
    ReportSerializer,
    RevenueReportSerializer,
    UserGrowthReportSerializer,
)


class ReportViewSet(viewsets.ModelViewSet):
    """Report management API"""

    queryset = Report.objects.all()
    serializer_class = ReportSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter reports based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return Report.objects.none()

        if not self.request.user.is_authenticated:
            return Report.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return Report.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return Report.objects.filter(reseller__user=self.request.user)
        return Report.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return ReportCreateSerializer
        return ReportSerializer

    @action(detail=False, methods=["get"])
    def revenue_report(self, request):
        """Generate revenue report"""
        try:
            # TODO: Generate actual revenue report
            # This would aggregate data from orders, payments, etc.

            report_data = {
                "total_revenue": 0,
                "monthly_revenue": 0,
                "revenue_by_country": {},
                "revenue_trend": [],
            }

            serializer = RevenueReportSerializer(data=report_data)
            if serializer.is_valid():
                return create_success_response(
                    "Revenue report generated", data=serializer.data
                )
            else:
                return create_error_response(
                    "Invalid report data",
                    data=serializer.errors,
                    status_code=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            return create_error_response(
                f"Failed to generate revenue report: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=False, methods=["get"])
    def user_growth_report(self, request):
        """Generate user growth report"""
        try:
            # TODO: Generate actual user growth report
            # This would analyze user registration trends

            report_data = {
                "total_users": 0,
                "new_users_this_month": 0,
                "growth_rate": 0,
                "user_trend": [],
            }

            serializer = UserGrowthReportSerializer(data=report_data)
            if serializer.is_valid():
                return create_success_response(
                    "User growth report generated", data=serializer.data
                )
            else:
                return create_error_response(
                    "Invalid report data",
                    data=serializer.errors,
                    status_code=status.HTTP_400_BAD_REQUEST,
                )
        except Exception as e:
            return create_error_response(
                f"Failed to generate user growth report: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )


class AnalyticsEventViewSet(viewsets.ModelViewSet):
    """Analytics event management API"""

    queryset = AnalyticsEvent.objects.all()
    serializer_class = AnalyticsEventSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter events based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return AnalyticsEvent.objects.none()

        if not self.request.user.is_authenticated:
            return AnalyticsEvent.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return AnalyticsEvent.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return AnalyticsEvent.objects.filter(reseller__user=self.request.user)
        return AnalyticsEvent.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return AnalyticsEventCreateSerializer
        return AnalyticsEventSerializer

    @action(detail=False, methods=["get"])
    def event_statistics(self, request):
        """Get analytics event statistics"""
        queryset = self.get_queryset()

        # Event type statistics
        event_stats = (
            queryset.values("event_type").annotate(count=Count("id")).order_by("-count")
        )

        # Time-based statistics
        current_month = timezone.now().month
        monthly_events = queryset.filter(created_at__month=current_month)

        stats = {
            "total_events": queryset.count(),
            "monthly_events": monthly_events.count(),
            "event_types": list(event_stats),
        }

        return create_success_response("Event statistics retrieved", data=stats)


class PerformanceMetricViewSet(viewsets.ModelViewSet):
    """Performance metric management API"""

    queryset = PerformanceMetric.objects.all()
    serializer_class = PerformanceMetricSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Only admins can access performance metrics"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return PerformanceMetric.objects.none()

        if not self.request.user.is_authenticated:
            return PerformanceMetric.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return PerformanceMetric.objects.all()
        return PerformanceMetric.objects.none()

    @action(detail=False, methods=["get"])
    def system_performance(self, request):
        """Get system performance metrics"""
        try:
            # TODO: Generate actual system performance metrics
            # This would include API response times, database performance, etc.

            performance_data = {
                "api_response_time": 0,
                "database_query_time": 0,
                "memory_usage": 0,
                "cpu_usage": 0,
                "active_connections": 0,
            }

            return create_success_response(
                "System performance retrieved", data=performance_data
            )
        except Exception as e:
            return create_error_response(
                f"Failed to retrieve system performance: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )


class ReportScheduleViewSet(viewsets.ModelViewSet):
    """Report schedule management API"""

    queryset = ReportSchedule.objects.all()
    serializer_class = ReportScheduleSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Filter schedules based on user role"""
        # Handle Swagger schema generation
        if getattr(self, "swagger_fake_view", False):
            return ReportSchedule.objects.none()

        if not self.request.user.is_authenticated:
            return ReportSchedule.objects.none()

        if hasattr(self.request.user, "is_admin") and self.request.user.is_admin:
            return ReportSchedule.objects.all()
        elif (
            hasattr(self.request.user, "is_reseller") and self.request.user.is_reseller
        ):
            return ReportSchedule.objects.filter(reseller__user=self.request.user)
        return ReportSchedule.objects.none()

    def get_serializer_class(self):
        if self.action == "create":
            return ReportScheduleCreateSerializer
        return ReportScheduleSerializer

    @action(detail=True, methods=["post"])
    def enable_schedule(self, request, pk=None):
        """Enable a report schedule"""
        try:
            schedule = self.get_object()
            schedule.is_active = True
            schedule.save()
            return create_success_response("Schedule enabled successfully")
        except Exception as e:
            return create_error_response(
                f"Failed to enable schedule: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    @action(detail=True, methods=["post"])
    def disable_schedule(self, request, pk=None):
        """Disable a report schedule"""
        try:
            schedule = self.get_object()
            schedule.is_active = False
            schedule.save()
            return create_success_response("Schedule disabled successfully")
        except Exception as e:
            return create_error_response(
                f"Failed to disable schedule: {str(e)}",
                status_code=status.HTTP_400_BAD_REQUEST,
            )
