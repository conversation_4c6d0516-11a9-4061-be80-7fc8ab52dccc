from django.conf import settings
from django.db import models
from simple_history.models import HistoricalRecords


class ESIMPlan(models.Model):
    """
    eSIM plans available from TraveRoam
    """

    class PlanType(models.TextChoices):
        DATA_ONLY = "data_only", "Data Only"
        VOICE_DATA = "voice_data", "Voice & Data"
        UNLIMITED = "unlimited", "Unlimited"

    # Plan details
    name = models.CharField(max_length=200)
    description = models.TextField()
    country = models.CharField(max_length=100)
    region = models.CharField(max_length=100, blank=True, null=True)

    # Data and validity
    data_volume = models.Char<PERSON>ield(max_length=50)  # e.g., "1GB", "Unlimited"
    validity_days = models.PositiveIntegerField()
    plan_type = models.CharField(
        max_length=20, choices=PlanType.choices, default=PlanType.DATA_ONLY
    )

    # Pricing
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    reseller_price = models.DecimalField(max_digits=10, decimal_places=2)
    public_price = models.DecimalField(max_digits=10, decimal_places=2)

    # TraveRoam integration
    traveroam_plan_id = models.CharField(max_length=100, unique=True)
    is_active = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # History tracking
    history = HistoricalRecords()

    class Meta:
        db_table = "esim_plans"
        verbose_name = "eSIM Plan"
        verbose_name_plural = "eSIM Plans"

    def __str__(self):
        return f"{self.name} - {self.country} ({self.data_volume})"


class ESIM(models.Model):
    """
    Individual eSIM instances
    """

    class ESIMStatus(models.TextChoices):
        PENDING = "pending", "Pending"
        PROVISIONED = "provisioned", "Provisioned"
        ASSIGNED = "assigned", "Assigned"
        ACTIVATED = "activated", "Activated"
        EXPIRED = "expired", "Expired"
        CANCELLED = "cancelled", "Cancelled"

    # Relationships
    plan = models.ForeignKey(ESIMPlan, on_delete=models.CASCADE, related_name="esims")
    client = models.ForeignKey(
        "clients.Client",
        on_delete=models.CASCADE,
        related_name="esims",
        null=True,
        blank=True,
    )
    public_user = models.ForeignKey(
        "clients.PublicUser",
        on_delete=models.CASCADE,
        related_name="esims",
        null=True,
        blank=True,
    )
    reseller = models.ForeignKey(
        "resellers.Reseller",
        on_delete=models.CASCADE,
        related_name="esims",
        null=True,
        blank=True,
    )

    # eSIM details
    status = models.CharField(
        max_length=20, choices=ESIMStatus.choices, default=ESIMStatus.PENDING
    )
    qr_code = models.TextField(blank=True, null=True)  # QR code data
    activation_code = models.CharField(
        max_length=200, blank=True, null=True
    )  # SM-DP+ activation code

    # TraveRoam integration
    traveroam_esim_id = models.CharField(
        max_length=100, unique=True, blank=True, null=True
    )
    traveroam_order_id = models.CharField(max_length=100, blank=True, null=True)

    # Usage tracking
    data_used = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )  # in MB
    data_remaining = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )  # in MB

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    assigned_at = models.DateTimeField(null=True, blank=True)
    activated_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)

    # History tracking
    history = HistoricalRecords()

    class Meta:
        db_table = "esims"
        verbose_name = "eSIM"
        verbose_name_plural = "eSIMs"

    def __str__(self):
        user = self.client or self.public_user
        return f"eSIM {self.id} - {user} - {self.plan.name}"

    @property
    def user(self):
        return self.client or self.public_user

    @property
    def is_expired(self):
        if self.expires_at:
            from django.utils import timezone

            return timezone.now() > self.expires_at
        return False


class ESIMUsage(models.Model):
    """
    Track eSIM usage data
    """

    esim = models.ForeignKey(ESIM, on_delete=models.CASCADE, related_name="usage_logs")
    data_used = models.DecimalField(max_digits=10, decimal_places=2)  # in MB
    timestamp = models.DateTimeField(auto_now_add=True)
    location = models.CharField(max_length=100, blank=True, null=True)

    # TraveRoam webhook data
    webhook_data = models.JSONField(blank=True, null=True)

    class Meta:
        db_table = "esim_usage"
        ordering = ["-timestamp"]

    def __str__(self):
        return f"Usage for eSIM {self.esim.id} - {self.data_used}MB"


class TraveRoamWebhook(models.Model):
    """
    Store TraveRoam webhook callbacks
    """

    class WebhookType(models.TextChoices):
        ACTIVATION = "activation", "Activation"
        USAGE = "usage", "Usage"
        EXPIRY = "expiry", "Expiry"
        ERROR = "error", "Error"

    webhook_type = models.CharField(max_length=20, choices=WebhookType.choices)
    esim = models.ForeignKey(
        ESIM, on_delete=models.CASCADE, related_name="webhooks", null=True, blank=True
    )
    payload = models.JSONField()
    processed = models.BooleanField(default=False)
    processed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "traveroam_webhooks"
        ordering = ["-created_at"]

    def __str__(self):
        return f"Webhook {self.id} - {self.webhook_type}"


class ESIMDelivery(models.Model):
    """
    Track eSIM delivery to users
    """

    class DeliveryMethod(models.TextChoices):
        EMAIL = "email", "Email"
        SMS = "sms", "SMS"
        IN_APP = "in_app", "In App"
        PDF = "pdf", "PDF Download"

    class DeliveryStatus(models.TextChoices):
        PENDING = "pending", "Pending"
        SENT = "sent", "Sent"
        DELIVERED = "delivered", "Delivered"
        FAILED = "failed", "Failed"

    esim = models.OneToOneField(ESIM, on_delete=models.CASCADE, related_name="delivery")
    delivery_method = models.CharField(max_length=20, choices=DeliveryMethod.choices)
    status = models.CharField(
        max_length=20, choices=DeliveryStatus.choices, default=DeliveryStatus.PENDING
    )

    # Delivery details
    recipient_email = models.EmailField(blank=True, null=True)
    recipient_phone = models.CharField(max_length=15, blank=True, null=True)
    delivery_message = models.TextField(blank=True, null=True)

    # Timestamps
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "esim_deliveries"
        verbose_name = "eSIM Delivery"
        verbose_name_plural = "eSIM Deliveries"

    def __str__(self):
        return f"Delivery for eSIM {self.esim.id} - {self.delivery_method}"
